const express = require('express');
const axios = require('axios');
const bodyParser = require('body-parser');
const polyline = require('@mapbox/polyline');
const http = require('http');
const socketIO = require('socket.io');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const aiService = require('./services/ai_service');
const dbService = require('./services/database_service');
const driverGeneratorService = require('./services/driver_generator_service');
const rideService = require('./services/ride_service');
const logger = require('./utils/logger');

// Import authentication routes
const authRoutes = require('./routes/auth');
const enhancedRideRoutes = require('./routes/enhanced_ride_routes');
const aiFeaturesRoutes = require('./routes/ai_features_routes');

const app = express();
const PORT = process.env.PORT || 3000;

// Create HTTP server
const server = http.createServer(app);

// Initialize Socket.IO
const io = socketIO(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// In-memory storage for ride requests
const rideRequests = {};
const driverStatus = {};

// إنشاء سائقين افتراضيين متاحين عند بدء التشغيل
function createDefaultDrivers() {
  const defaultDrivers = [
    { id: 'driver_1', name: 'أحمد محمد', rating: 4.8, car: 'تويوتا كورولا', plate: 'ABC 123' },
    { id: 'driver_2', name: 'محمود علي', rating: 4.5, car: 'هيونداي إلنترا', plate: 'XYZ 789' },
    { id: 'driver_3', name: 'خالد إبراهيم', rating: 4.9, car: 'نيسان صني', plate: 'DEF 456' },
  ];

  defaultDrivers.forEach(driver => {
    driverStatus[driver.id] = {
      socketId: null,
      available: true,
      info: {
        name: driver.name,
        rating: driver.rating,
        car: driver.car,
        plate: driver.plate,
        eta: `${Math.floor(Math.random() * 10) + 2} دقائق`
      }
    };
  });

  console.log(`Created ${defaultDrivers.length} default drivers`);
}

// استدعاء الدالة لإنشاء السائقين الافتراضيين
createDefaultDrivers();

// وظيفة لمحاكاة حركة السائق وإرسال تحديثات الموقع
function simulateDriverMovement(rideId, driverId) {
  if (!rideRequests[rideId]) {
    console.log(`Cannot simulate driver movement: Ride ${rideId} not found`);
    return;
  }

  const ride = rideRequests[rideId];
  const pickupLocation = ride.pickupLocation;
  const destination = ride.destination;

  // إنشاء مسار وهمي بين موقع السائق الحالي ونقطة الالتقاط
  const startLat = pickupLocation.lat - 0.01;
  const startLng = pickupLocation.lng - 0.01;

  // عدد النقاط في المسار
  const numPoints = 10;

  // إنشاء مسار من موقع السائق الحالي إلى نقطة الالتقاط
  const pickupPath = [];
  for (let i = 0; i <= numPoints; i++) {
    const ratio = i / numPoints;
    pickupPath.push({
      lat: startLat + (pickupLocation.lat - startLat) * ratio,
      lng: startLng + (pickupLocation.lng - startLng) * ratio
    });
  }

  // إنشاء مسار من نقطة الالتقاط إلى الوجهة
  const destinationPath = [];
  for (let i = 0; i <= numPoints; i++) {
    const ratio = i / numPoints;
    destinationPath.push({
      lat: pickupLocation.lat + (destination.lat - pickupLocation.lat) * ratio,
      lng: pickupLocation.lng + (destination.lng - pickupLocation.lng) * ratio
    });
  }

  // حفظ معلومات المسار في كائن الرحلة
  ride.routeInfo = {
    pickupPath: pickupPath,
    destinationPath: destinationPath,
    totalPoints: pickupPath.length + destinationPath.length
  };

  // إرسال تحديثات الموقع على فترات منتظمة
  let pickupIndex = 0;
  let destinationIndex = 0;
  let phase = 'pickup'; // 'pickup' أو 'destination'

  const movementInterval = setInterval(() => {
    if (!rideRequests[rideId]) {
      console.log(`Stopping driver movement simulation: Ride ${rideId} no longer exists`);
      clearInterval(movementInterval);
      return;
    }

    let currentLocation;
    let currentIndex;
    let totalProgress;

    if (phase === 'pickup') {
      // السائق في طريقه إلى نقطة الالتقاط
      if (pickupIndex < pickupPath.length) {
        currentLocation = pickupPath[pickupIndex];
        currentIndex = pickupIndex;
        totalProgress = pickupIndex;
        pickupIndex++;
      } else {
        // وصل السائق إلى نقطة الالتقاط
        console.log(`Driver ${driverId} arrived at pickup location for ride ${rideId}`);
        phase = 'destination';

        // إرسال إشعار بوصول السائق إلى نقطة الالتقاط
        io.to(ride.riderSocketId).emit('driver:arrived_at_pickup', {
          rideId,
          driverId,
          message: 'وصل السائق إلى نقطة الالتقاط',
          location: pickupLocation
        });

        // تأخير قصير قبل بدء الرحلة
        setTimeout(() => {
          // تحديث حالة الرحلة إلى in_progress
          rideRequests[rideId].status = 'in_progress';

          // إرسال إشعار بدء الرحلة للراكب مع معلومات إضافية
          io.to(ride.riderSocketId).emit('ride:started', {
            rideId,
            driverId,
            timestamp: new Date().toISOString(),
            pickupLocation: pickupLocation,
            destination: destination,
            routeInfo: ride.routeInfo
          });
        }, 2000);

        // الاستمرار في الحلقة الحالية
        return;
      }
    } else {
      // السائق في طريقه إلى الوجهة
      if (destinationIndex < destinationPath.length) {
        currentLocation = destinationPath[destinationIndex];
        currentIndex = destinationIndex;
        totalProgress = pickupPath.length + destinationIndex;
        destinationIndex++;
      } else {
        // وصل السائق إلى الوجهة
        console.log(`Driver ${driverId} arrived at destination for ride ${rideId}`);

        // إرسال إشعار بوصول السائق إلى الوجهة
        io.to(ride.riderSocketId).emit('driver:arrived_at_destination', {
          rideId,
          driverId,
          message: 'وصل السائق إلى الوجهة',
          location: destination
        });

        // تحديث حالة الرحلة إلى completed
        rideRequests[rideId].status = 'completed';
        rideRequests[rideId].completedAt = new Date();

        io.to(ride.riderSocketId).emit('ride:completed', {
          rideId,
          timestamp: new Date().toISOString(),
          totalDistance: calculateSimpleDistance(
            pickupLocation.lat, pickupLocation.lng,
            destination.lat, destination.lng
          ),
          totalDuration: (new Date() - new Date(rideRequests[rideId].startedAt)) / 1000 // بالثواني
        });

        // إعادة تعيين السائق كمتاح
        driverStatus[driverId].available = true;
        delete driverStatus[driverId].currentRideId;

        // إيقاف المحاكاة
        clearInterval(movementInterval);
        return;
      }
    }

    // حساب النسبة المئوية للتقدم
    const progressPercentage = Math.round(
      (totalProgress / (pickupPath.length + destinationPath.length)) * 100
    );

    // إرسال تحديث الموقع
    io.to(ride.riderSocketId).emit('driver:location_update', {
      rideId,
      driverId,
      location: currentLocation,
      phase: phase,
      progress: {
        current: currentIndex,
        total: phase === 'pickup' ? pickupPath.length : destinationPath.length,
        percentage: progressPercentage
      },
      timestamp: new Date().toISOString()
    });

    console.log(`Driver ${driverId} location update for ride ${rideId}: ${JSON.stringify(currentLocation)}, Progress: ${progressPercentage}%`);
  }, 2000); // تحديث كل 2 ثانية

  // حفظ مرجع المؤقت في كائن الرحلة
  rideRequests[rideId].movementInterval = movementInterval;
}

// Middleware
app.use(cors({
  origin: "*",
  methods: ["GET", "POST", "PUT", "DELETE"],
  allowedHeaders: ["Content-Type", "Authorization"]
}));
app.use(bodyParser.json());
app.use(express.static('public'));

// Authentication routes
app.use('/auth', authRoutes);

// Enhanced ride routes
app.use('/api/enhanced-rides', (req, res, next) => {
  req.io = io; // Pass socket.io instance to routes
  next();
}, enhancedRideRoutes);

// AI Features routes
app.use('/api/ai', (req, res, next) => {
  req.io = io; // Pass socket.io instance to routes
  next();
}, aiFeaturesRoutes);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('New client connected:', socket.id);

  // Driver registers as available
  socket.on('driver:available', (driverId) => {
    driverStatus[driverId] = { socketId: socket.id, available: true };
    console.log(`Driver ${driverId} is now available`);
  });

  // Driver registers as unavailable
  socket.on('driver:unavailable', (driverId) => {
    if (driverStatus[driverId]) {
      driverStatus[driverId].available = false;
      console.log(`Driver ${driverId} is now unavailable`);
    }
  });

  // Rider creates a ride request
  socket.on('ride:request', (data) => {
    const rideId = uuidv4();
    const rideRequest = {
      id: rideId,
      riderId: data.riderId,
      pickupLocation: data.pickupLocation,
      destination: data.destination,
      status: 'pending',
      timestamp: new Date(),
      riderSocketId: socket.id
    };

    // إضافة معرف السائق المحدد إذا تم توفيره
    if (data.specificDriverId) {
      rideRequest.specificDriverId = data.specificDriverId;
      console.log(`Ride request for specific driver: ${data.specificDriverId}`);
    }

    rideRequests[rideId] = rideRequest;
    console.log(`New ride request created: ${rideId}`);

    // إذا كان هناك سائق محدد، أرسل الطلب إلى هذا السائق فقط
    if (data.specificDriverId) {
      const driverId = data.specificDriverId;
      if (driverStatus[driverId] && driverStatus[driverId].available) {
        // إذا كان السائق متاحًا ولكن ليس لديه socketId (سائق افتراضي)
        // قم بمحاكاة قبول الطلب تلقائيًا بعد فترة قصيرة
        if (!driverStatus[driverId].socketId) {
          console.log(`Auto-accepting ride for default driver: ${driverId}`);

          // تأخير قصير لمحاكاة وقت استجابة السائق
          setTimeout(() => {
            // تحديث حالة الرحلة
            rideRequests[rideId].status = 'accepted';
            rideRequests[rideId].driverId = driverId;
            rideRequests[rideId].acceptedAt = new Date();

            // إرسال إشعار قبول الرحلة للراكب
            const timestamp = new Date();
            socket.emit('ride:accepted', {
              rideId,
              driverId,
              timestamp: timestamp.toISOString(),
              message: 'Your ride has been accepted',
              driverInfo: driverStatus[driverId].info,
              // إضافة معلومات الموقع الحالي للسائق
              driverLocation: {
                lat: rideRequests[rideId].pickupLocation.lat - (Math.random() * 0.01),
                lng: rideRequests[rideId].pickupLocation.lng - (Math.random() * 0.01),
              }
            });

            console.log(`Ride ${rideId} auto-accepted by default driver ${driverId}`);

            // تعيين السائق كغير متاح
            driverStatus[driverId].available = false;
            driverStatus[driverId].currentRideId = rideId;

            // بدء محاكاة تحديثات موقع السائق
            simulateDriverMovement(rideId, driverId);
          }, 2000); // تأخير 2 ثانية

          console.log(`Ride request will be auto-accepted by default driver: ${driverId}`);
        } else {
          // إذا كان السائق متصلاً، أرسل له طلب الرحلة
          io.to(driverStatus[driverId].socketId).emit('ride:new_request', rideRequest);
          console.log(`Ride request sent to specific driver: ${driverId}`);
        }
      } else {
        // إذا كان السائق المحدد غير متاح، أرسل إشعارًا للراكب
        socket.emit('ride:driver_unavailable', {
          rideId,
          driverId: data.specificDriverId,
          message: 'السائق المحدد غير متاح حاليًا'
        });
        console.log(`Specific driver ${driverId} is unavailable`);
      }
    } else {
      // إذا لم يتم تحديد سائق، أرسل الطلب إلى جميع السائقين المتاحين
      let availableDrivers = Object.keys(driverStatus).filter(driverId =>
        driverStatus[driverId].available
      );

      if (availableDrivers.length > 0) {
        // اختيار سائق افتراضي عشوائي من السائقين المتاحين
        const randomIndex = Math.floor(Math.random() * availableDrivers.length);
        const selectedDriverId = availableDrivers[randomIndex];

        console.log(`Auto-selecting driver ${selectedDriverId} for ride ${rideId}`);

        // تأخير قصير لمحاكاة وقت البحث عن سائق
        setTimeout(() => {
          // تحديث حالة الرحلة
          rideRequests[rideId].status = 'accepted';
          rideRequests[rideId].driverId = selectedDriverId;
          rideRequests[rideId].acceptedAt = new Date();

          // إرسال إشعار قبول الرحلة للراكب
          const timestamp = new Date();
          socket.emit('ride:accepted', {
            rideId,
            driverId: selectedDriverId,
            timestamp: timestamp.toISOString(),
            message: 'Your ride has been accepted',
            driverInfo: driverStatus[selectedDriverId].info,
            // إضافة معلومات الموقع الحالي للسائق
            driverLocation: {
              lat: rideRequests[rideId].pickupLocation.lat - (Math.random() * 0.01),
              lng: rideRequests[rideId].pickupLocation.lng - (Math.random() * 0.01),
            }
          });

          console.log(`Ride ${rideId} auto-accepted by default driver ${selectedDriverId}`);

          // تعيين السائق كغير متاح
          driverStatus[selectedDriverId].available = false;
          driverStatus[selectedDriverId].currentRideId = rideId;

          // بدء محاكاة تحديثات موقع السائق
          simulateDriverMovement(rideId, selectedDriverId);
        }, 3000); // تأخير 3 ثوانٍ
      } else {
        // إذا لم يكن هناك سائقين متاحين
        socket.emit('ride:no_drivers_available', {
          rideId,
          message: 'لا يوجد سائقين متاحين حاليًا، يرجى المحاولة مرة أخرى لاحقًا'
        });
        console.log(`No drivers available for ride ${rideId}`);
      }
    }

    // إرسال تأكيد إنشاء الرحلة
    socket.emit('ride:created', { rideId });
  });

  // Driver accepts a ride
  socket.on('ride:accept', async (data) => {
    const { rideId, driverId } = data;
    const timestamp = new Date();

    if (!rideRequests[rideId]) {
      console.log(`Error: Ride ${rideId} not found for acceptance`);
      socket.emit('ride:error', {
        rideId,
        error: 'ride_not_found',
        message: `الرحلة رقم ${rideId} غير موجودة أو تم إلغاؤها`
      });
      return;
    }

    // التحقق من حالة الرحلة
    if (rideRequests[rideId].status !== 'pending') {
      console.log(`Error: Ride ${rideId} is not pending (status: ${rideRequests[rideId].status})`);
      socket.emit('ride:error', {
        rideId,
        error: 'invalid_ride_status',
        message: `الرحلة رقم ${rideId} ليست في حالة انتظار (الحالة الحالية: ${rideRequests[rideId].status})`
      });
      return;
    }

    // Check if driver is already on a ride
    const isDriverOnRide = Object.values(rideRequests).some(
      ride => ride.driverId === driverId &&
      (ride.status === 'accepted' || ride.status === 'in_progress') &&
      ride.id !== rideId
    );

    // If driver is on a ride, analyze if they can accept this ride
    let sharingAnalysis = null;
    if (isDriverOnRide) {
      try {
        sharingAnalysis = await rideService.analyzeRideSharing(driverId, {
          riderId: rideRequests[rideId].riderId,
          pickupLocation: rideRequests[rideId].pickupLocation,
          destination: rideRequests[rideId].destination
        });

        // If the analysis recommends rejecting, inform the driver
        if (!sharingAnalysis.canAccept) {
          socket.emit('ride:sharing_analysis', {
            rideId,
            analysis: sharingAnalysis,
            message: 'This ride would cause a significant detour. Are you sure you want to accept?',
            requireConfirmation: true
          });
          return; // Wait for driver's confirmation
        }
      } catch (error) {
        logger.error(`Error analyzing ride sharing: ${error.message}`);
        // Continue with acceptance if analysis fails
      }
    }

    // Update ride status
    rideRequests[rideId].status = 'accepted';
    rideRequests[rideId].driverId = driverId;
    rideRequests[rideId].acceptedAt = timestamp;

    console.log(`Ride ${rideId} accepted by driver ${driverId} at ${timestamp.toISOString()}`);

    // الحصول على معلومات السائق
    let driverInfo;
    if (driverStatus[driverId] && driverStatus[driverId].info) {
      // استخدام معلومات السائق المخزنة مسبقًا
      driverInfo = driverStatus[driverId].info;
    } else {
      // إنشاء معلومات افتراضية للسائق
      driverInfo = {
        name: `Driver ${driverId.substring(0, 5)}`,
        rating: (Math.random() * 2 + 3).toFixed(1), // تقييم عشوائي بين 3 و 5
        car: 'Toyota Corolla',
        plate: `ABC ${Math.floor(Math.random() * 1000)}`,
        eta: `${Math.floor(Math.random() * 10) + 2} minutes`, // وقت وصول عشوائي
      };
    }

    // If driver is already on a ride, add this ride as a stop to the existing ride
    if (isDriverOnRide && sharingAnalysis) {
      // Find the current ride
      const currentRideId = Object.values(rideRequests).find(
        ride => ride.driverId === driverId &&
        (ride.status === 'accepted' || ride.status === 'in_progress') &&
        ride.id !== rideId
      )?.id;

      if (currentRideId) {
        try {
          // Add the new ride as stops to the current ride
          await rideService.addStopToRide(currentRideId, {
            riderId: rideRequests[rideId].riderId,
            pickupLocation: rideRequests[rideId].pickupLocation,
            destination: rideRequests[rideId].destination
          });

          // Mark this ride as merged
          rideRequests[rideId].mergedWithRideId = currentRideId;
          rideRequests[rideId].status = 'merged';

          // Notify the rider about the shared ride
          io.to(rideRequests[rideId].riderSocketId).emit('ride:shared', {
            rideId,
            mergedWithRideId: currentRideId,
            driverId,
            timestamp: timestamp.toISOString(),
            message: 'Your ride will be shared with another passenger',
            driverInfo: driverInfo,
            // إضافة معلومات الموقع الحالي للسائق
            driverLocation: {
              lat: rideRequests[rideId].pickupLocation.lat - (Math.random() * 0.01),
              lng: rideRequests[rideId].pickupLocation.lng - (Math.random() * 0.01),
            }
          });

          console.log(`Ride ${rideId} merged with ride ${currentRideId}`);
          return;
        } catch (error) {
          logger.error(`Error merging rides: ${error.message}`);
          // Continue with normal acceptance if merging fails
        }
      }
    }

    // Notify the rider immediately with enhanced data
    io.to(rideRequests[rideId].riderSocketId).emit('ride:accepted', {
      rideId,
      driverId,
      timestamp: timestamp.toISOString(),
      message: 'Your ride has been accepted',
      driverInfo: driverInfo,
      // إضافة معلومات الموقع الحالي للسائق
      driverLocation: {
        lat: rideRequests[rideId].pickupLocation.lat - (Math.random() * 0.01),
        lng: rideRequests[rideId].pickupLocation.lng - (Math.random() * 0.01),
      }
    });

    // Mark driver as unavailable
    if (driverStatus[driverId]) {
      driverStatus[driverId].available = false;
      driverStatus[driverId].currentRideId = rideId;
    }

    // إرسال إشعار لجميع السائقين الآخرين بأن الرحلة تم قبولها
    Object.keys(driverStatus).forEach(otherDriverId => {
      if (otherDriverId !== driverId && driverStatus[otherDriverId].socketId) {
        io.to(driverStatus[otherDriverId].socketId).emit('ride:taken', {
          rideId,
          driverId,
          timestamp: timestamp.toISOString()
        });
      }
    });

    // بدء محاكاة حركة السائق إذا كان سائقًا افتراضيًا
    if (!driverStatus[driverId].socketId) {
      simulateDriverMovement(rideId, driverId);
    }

    // Make the ride visible to other users for AI ride sharing
    // This allows the system to suggest ride sharing opportunities
    Object.keys(rideRequests).forEach(otherRideId => {
      if (otherRideId !== rideId && rideRequests[otherRideId].status === 'pending') {
        // Notify riders with pending requests about this active ride
        io.to(rideRequests[otherRideId].riderSocketId).emit('ride:sharing_opportunity', {
          activeRideId: rideId,
          pickupLocation: rideRequests[rideId].pickupLocation,
          destination: rideRequests[rideId].destination,
          driverId: driverId
        });
      }
    });

    console.log(`Ride ${rideId} is now visible for ride sharing opportunities`);

    // Simulate driver location updates
    simulateDriverLocationUpdates(rideRequests[rideId], driverId);
  });

  // Fonction pour simuler les mises à jour de position du conducteur
  function simulateDriverLocationUpdates(ride, driverId) {
    // Position initiale du conducteur (légèrement éloignée du point de ramassage)
    const driverStartLat = ride.pickupLocation.lat - 0.005;
    const driverStartLng = ride.pickupLocation.lng - 0.005;

    // Calculer le nombre d'étapes pour atteindre le point de ramassage
    const steps = 10;
    const latStep = (ride.pickupLocation.lat - driverStartLat) / steps;
    const lngStep = (ride.pickupLocation.lng - driverStartLng) / steps;

    // Envoyer des mises à jour de position toutes les 2 secondes
    let currentStep = 0;

    const locationInterval = setInterval(() => {
      if (currentStep <= steps) {
        // Calculer la position actuelle
        const currentLat = driverStartLat + (latStep * currentStep);
        const currentLng = driverStartLng + (lngStep * currentStep);

        // Envoyer la mise à jour de position
        io.to(ride.riderSocketId).emit('driver:location_update', {
          driverId,
          location: {
            lat: currentLat,
            lng: currentLng
          }
        });

        currentStep++;
      } else {
        // Une fois arrivé au point de ramassage, simuler le trajet vers la destination
        if (ride.status === 'accepted') {
          // Mettre à jour le statut de la course
          ride.status = 'in_progress';

          // Envoyer une notification de démarrage de la course
          io.to(ride.riderSocketId).emit('ride:started', { rideId: ride.id });

          // Simuler le trajet vers la destination
          simulateRideToDestination(ride, driverId);
        }

        // Arrêter cet intervalle
        clearInterval(locationInterval);
      }
    }, 2000);
  }

  // وظيفة لمحاكاة الرحلة من نقطة الالتقاط إلى الوجهة
  function simulateRideToDestination(ride, driverId) {
    if (!ride || !ride.pickupLocation || !ride.destination) {
      console.log(`Cannot simulate ride to destination: Invalid ride data`);
      return;
    }

    const rideId = ride.id;
    const pickupLocation = ride.pickupLocation;
    const destination = ride.destination;

    // إذا كانت معلومات المسار متوفرة، استخدمها
    if (ride.routeInfo && ride.routeInfo.destinationPath) {
      const destinationPath = ride.routeInfo.destinationPath;
      let destinationIndex = 0;

      console.log(`Starting ride simulation from pickup to destination for ride ${rideId}`);

      // تحديث حالة الرحلة إلى in_progress إذا لم تكن كذلك بالفعل
      if (ride.status !== 'in_progress') {
        ride.status = 'in_progress';
        ride.startedAt = new Date();

        // إرسال إشعار بدء الرحلة للراكب
        io.to(ride.riderSocketId).emit('ride:started', {
          rideId,
          driverId,
          timestamp: new Date().toISOString(),
          pickupLocation: pickupLocation,
          destination: destination,
          routeInfo: ride.routeInfo
        });
      }

      // إرسال تحديثات الموقع على فترات منتظمة
      const movementInterval = setInterval(() => {
        if (!rideRequests[rideId]) {
          console.log(`Stopping ride simulation: Ride ${rideId} no longer exists`);
          clearInterval(movementInterval);
          return;
        }

        if (destinationIndex < destinationPath.length) {
          const currentLocation = destinationPath[destinationIndex];

          // حساب النسبة المئوية للتقدم
          const progressPercentage = Math.round(
            (destinationIndex / destinationPath.length) * 100
          );

          // إرسال تحديث الموقع
          io.to(ride.riderSocketId).emit('driver:location_update', {
            rideId,
            driverId,
            location: currentLocation,
            phase: 'destination',
            progress: {
              current: destinationIndex,
              total: destinationPath.length,
              percentage: progressPercentage
            },
            timestamp: new Date().toISOString()
          });

          console.log(`Driver ${driverId} location update for ride ${rideId}: ${JSON.stringify(currentLocation)}, Progress: ${progressPercentage}%`);

          destinationIndex++;
        } else {
          // وصل السائق إلى الوجهة
          console.log(`Driver ${driverId} arrived at destination for ride ${rideId}`);

          // إرسال إشعار بوصول السائق إلى الوجهة
          io.to(ride.riderSocketId).emit('driver:arrived_at_destination', {
            rideId,
            driverId,
            message: 'وصل السائق إلى الوجهة',
            location: destination
          });

          // تحديث حالة الرحلة إلى completed
          ride.status = 'completed';
          ride.completedAt = new Date();

          // حساب المسافة والوقت
          const totalDistance = calculateSimpleDistance(
            pickupLocation.lat, pickupLocation.lng,
            destination.lat, destination.lng
          );

          const totalDuration = (new Date() - new Date(ride.startedAt)) / 1000; // بالثواني

          // إرسال إشعار بانتهاء الرحلة
          io.to(ride.riderSocketId).emit('ride:completed', {
            rideId,
            timestamp: new Date().toISOString(),
            totalDistance,
            totalDuration
          });

          // إعادة تعيين السائق كمتاح
          if (driverStatus[driverId]) {
            driverStatus[driverId].available = true;
            delete driverStatus[driverId].currentRideId;
          }

          // إيقاف المحاكاة
          clearInterval(movementInterval);
        }
      }, 2000); // تحديث كل 2 ثانية

      // حفظ مرجع المؤقت في كائن الرحلة
      ride.movementInterval = movementInterval;
    } else {
      // إذا لم تكن معلومات المسار متوفرة، استخدم الطريقة القديمة
      console.log(`Using legacy route simulation for ride ${rideId}`);

      // حساب عدد الخطوات للوصول إلى الوجهة
      const steps = 15;
      const latStep = (destination.lat - pickupLocation.lat) / steps;
      const lngStep = (destination.lng - pickupLocation.lng) / steps;

      // الموقع الأولي (نقطة الالتقاط)
      let currentLat = pickupLocation.lat;
      let currentLng = pickupLocation.lng;

      // إرسال تحديثات الموقع كل 2 ثانية
      let currentStep = 0;

      const locationInterval = setInterval(() => {
        if (!rideRequests[rideId]) {
          console.log(`Stopping ride simulation: Ride ${rideId} no longer exists`);
          clearInterval(locationInterval);
          return;
        }

        if (currentStep <= steps) {
          // حساب الموقع الحالي
          currentLat += latStep;
          currentLng += lngStep;

          // حساب النسبة المئوية للتقدم
          const progressPercentage = Math.round((currentStep / steps) * 100);

          // إرسال تحديث الموقع
          io.to(ride.riderSocketId).emit('driver:location_update', {
            rideId,
            driverId,
            location: {
              lat: currentLat,
              lng: currentLng
            },
            phase: 'destination',
            progress: {
              current: currentStep,
              total: steps,
              percentage: progressPercentage
            },
            timestamp: new Date().toISOString()
          });

          console.log(`Driver ${driverId} location update for ride ${rideId}: {lat: ${currentLat}, lng: ${currentLng}}, Progress: ${progressPercentage}%`);

          currentStep++;
        } else {
          // وصل السائق إلى الوجهة
          console.log(`Driver ${driverId} arrived at destination for ride ${rideId}`);

          // إرسال إشعار بوصول السائق إلى الوجهة
          io.to(ride.riderSocketId).emit('driver:arrived_at_destination', {
            rideId,
            driverId,
            message: 'وصل السائق إلى الوجهة',
            location: destination
          });

          // تحديث حالة الرحلة إلى completed
          ride.status = 'completed';
          ride.completedAt = new Date();

          // إرسال إشعار بانتهاء الرحلة
          io.to(ride.riderSocketId).emit('ride:completed', {
            rideId,
            timestamp: new Date().toISOString()
          });

          // إعادة تعيين السائق كمتاح
          if (driverStatus[driverId]) {
            driverStatus[driverId].available = true;
            delete driverStatus[driverId].currentRideId;
          }

          // إيقاف المحاكاة
          clearInterval(locationInterval);
        }
      }, 2000);

      // حفظ مرجع المؤقت في كائن الرحلة
      ride.movementInterval = locationInterval;
    }
  }

  // Driver rejects a ride
  socket.on('ride:reject', (data) => {
    const { rideId, driverId } = data;

    if (!rideRequests[rideId]) {
      console.log(`Error: Ride ${rideId} not found for rejection`);
      socket.emit('ride:error', {
        rideId,
        error: 'ride_not_found',
        message: `الرحلة رقم ${rideId} غير موجودة أو تم إلغاؤها`
      });
      return;
    }

    console.log(`Ride ${rideId} rejected by driver ${driverId}`);

    // Update ride status in memory
    rideRequests[rideId].status = 'pending'; // Keep it pending so other drivers can accept
    rideRequests[rideId].rejectedBy = rideRequests[rideId].rejectedBy || [];
    rideRequests[rideId].rejectedBy.push(driverId);

    // Add rejection timestamp
    rideRequests[rideId].rejectionTime = new Date();

    // Notify the rider
    io.to(rideRequests[rideId].riderSocketId).emit('ride:rejected', {
      rideId,
      driverId,
      timestamp: new Date(),
      message: 'Driver rejected your ride request'
    });

    // تحقق مما إذا كان هناك سائقين متاحين آخرين
    const availableDrivers = Object.keys(driverStatus).filter(otherDriverId =>
      driverStatus[otherDriverId].available &&
      otherDriverId !== driverId &&
      (!rideRequests[rideId].rejectedBy || !rideRequests[rideId].rejectedBy.includes(otherDriverId))
    );

    if (availableDrivers.length > 0) {
      // اختيار سائق عشوائي من السائقين المتاحين
      const randomIndex = Math.floor(Math.random() * availableDrivers.length);
      const selectedDriverId = availableDrivers[randomIndex];

      console.log(`Auto-selecting driver ${selectedDriverId} for ride ${rideId} after rejection`);

      // تأخير قصير لمحاكاة وقت البحث عن سائق
      setTimeout(() => {
        // التحقق من أن الرحلة لا تزال موجودة وفي حالة انتظار
        if (rideRequests[rideId] && rideRequests[rideId].status === 'pending') {
          // تحديث حالة الرحلة
          rideRequests[rideId].status = 'accepted';
          rideRequests[rideId].driverId = selectedDriverId;
          rideRequests[rideId].acceptedAt = new Date();

          // إرسال إشعار قبول الرحلة للراكب
          const timestamp = new Date();
          io.to(rideRequests[rideId].riderSocketId).emit('ride:accepted', {
            rideId,
            driverId: selectedDriverId,
            timestamp: timestamp.toISOString(),
            message: 'Your ride has been accepted',
            driverInfo: driverStatus[selectedDriverId].info,
            // إضافة معلومات الموقع الحالي للسائق
            driverLocation: {
              lat: rideRequests[rideId].pickupLocation.lat - (Math.random() * 0.01),
              lng: rideRequests[rideId].pickupLocation.lng - (Math.random() * 0.01),
            }
          });

          console.log(`Ride ${rideId} auto-accepted by default driver ${selectedDriverId} after rejection`);

          // تعيين السائق كغير متاح
          driverStatus[selectedDriverId].available = false;
          driverStatus[selectedDriverId].currentRideId = rideId;

          // بدء محاكاة تحديثات موقع السائق
          simulateDriverMovement(rideId, selectedDriverId);
        }
      }, 3000); // تأخير 3 ثوانٍ
    } else {
      // إذا لم يكن هناك سائقين متاحين آخرين
      io.to(rideRequests[rideId].riderSocketId).emit('ride:no_drivers_available', {
        rideId,
        message: 'لا يوجد سائقين متاحين حاليًا، يرجى المحاولة مرة أخرى لاحقًا'
      });
      console.log(`No more available drivers for ride ${rideId} after rejection`);
    }
  });

  // Driver starts the ride
  socket.on('ride:start', (data) => {
    const { rideId, driverId } = data;

    if (rideRequests[rideId]) {
      rideRequests[rideId].status = 'in_progress';
      rideRequests[rideId].startedAt = new Date();
      console.log(`Ride ${rideId} started by driver ${driverId}`);

      // إرسال إشعار بدء الرحلة للراكب مع معلومات إضافية
      io.to(rideRequests[rideId].riderSocketId).emit('ride:started', {
        rideId,
        driverId,
        timestamp: new Date().toISOString(),
        pickupLocation: rideRequests[rideId].pickupLocation,
        destination: rideRequests[rideId].destination,
        // إرسال معلومات المسار إذا كانت متوفرة
        routeInfo: rideRequests[rideId].routeInfo || null
      });

      // إذا كان السائق افتراضيًا، قم بمحاكاة الرحلة
      if (driverStatus[driverId] && !driverStatus[driverId].socketId) {
        // إيقاف محاكاة الحركة السابقة إذا كانت موجودة
        if (rideRequests[rideId].movementInterval) {
          clearInterval(rideRequests[rideId].movementInterval);
        }

        // بدء محاكاة الرحلة من نقطة الالتقاط إلى الوجهة
        simulateRideToDestination(rideRequests[rideId], driverId);
      }
    } else {
      console.log(`Error: Ride ${rideId} not found for starting`);
      socket.emit('ride:error', {
        rideId,
        error: 'ride_not_found',
        message: `الرحلة رقم ${rideId} غير موجودة أو تم إلغاؤها`
      });
    }
  });

  // Driver completes the ride
  socket.on('ride:complete', (data) => {
    const { rideId, driverId } = data;

    if (rideRequests[rideId]) {
      rideRequests[rideId].status = 'completed';
      console.log(`Ride ${rideId} completed`);

      // Notify the rider
      io.to(rideRequests[rideId].riderSocketId).emit('ride:completed', { rideId });

      // Mark driver as available again
      if (driverStatus[driverId]) {
        driverStatus[driverId].available = true;
      }
    }
  });

  // Driver location updates
  socket.on('driver:location', (data) => {
    const { rideId, driverId, location } = data;

    console.log(`Driver ${driverId} location update for ride ${rideId}:`, location);

    if (rideRequests[rideId]) {
      // Update the ride's current location
      rideRequests[rideId].currentLocation = location;

      // Calculate and update ETA if we have destination
      if (rideRequests[rideId].destination) {
        // In a real app, you would use a routing service to calculate ETA
        // Here we'll use a simple approximation
        const distanceToDestination = calculateSimpleDistance(
          location.lat,
          location.lng,
          rideRequests[rideId].destination.lat,
          rideRequests[rideId].destination.lng
        );

        // Assuming average speed of 30 km/h
        const etaInMinutes = Math.round((distanceToDestination / 1000) / 30 * 60);
        rideRequests[rideId].eta = etaInMinutes;

        console.log(`Updated ETA for ride ${rideId}: ${etaInMinutes} minutes`);
      }

      // Forward driver's location to the rider
      io.to(rideRequests[rideId].riderSocketId).emit('driver:location_update', {
        rideId,
        driverId,
        location,
        eta: rideRequests[rideId].eta
      });

      // Forward to any shared riders
      if (rideRequests[rideId].sharedRiders && rideRequests[rideId].sharedRiders.length > 0) {
        rideRequests[rideId].sharedRiders.forEach(sharedRider => {
          if (sharedRider.socketId) {
            io.to(sharedRider.socketId).emit('driver:location_update', {
              rideId,
              driverId,
              location,
              eta: rideRequests[rideId].eta
            });
          }
        });
      }

      // Update driver status
      if (driverStatus[driverId]) {
        driverStatus[driverId].location = location;
      }
    }
  });

  // Simple distance calculation using Haversine formula
  function calculateSimpleDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Earth radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distance in meters
  }

  // Ride sharing request
  socket.on('ride:sharing_request', async (data) => {
    const { rideId, newRiderId, newPickupLocation, newDestination } = data;

    if (rideRequests[rideId] && rideRequests[rideId].status === 'in_progress') {
      try {
        console.log(`Processing ride sharing request for ride ${rideId} from rider ${newRiderId}`);

        // Calculate original route distance
        const originalDistance = await calculateRouteDistance(
          rideRequests[rideId].pickupLocation,
          rideRequests[rideId].destination
        );

        // Calculate new route with detour
        const newRouteDistance = await calculateRouteDistance(
          rideRequests[rideId].currentLocation || rideRequests[rideId].pickupLocation,
          newPickupLocation,
          newDestination,
          rideRequests[rideId].destination
        );

        // Calculate new rider direct distance
        const newRiderDistance = await calculateRouteDistance(
          newPickupLocation,
          newDestination
        );

        console.log('Calling AI service with distances:', {
          originalDistance,
          newRouteDistance,
          newRiderDistance
        });

        // Use AI service to predict if ride sharing is beneficial
        const prediction = await aiService.predictRideSharing(
          originalDistance,
          newRouteDistance,
          newRiderDistance
        );

        console.log('AI prediction result:', prediction);

        // Calculate fare details
        const baseFare = 10; // Base fare in currency units
        const perKmRate = 2; // Rate per km in currency units

        // Original rider's fare
        const originalFare = baseFare + ((originalDistance / 1000) * perKmRate);

        // New rider's direct fare (if they took their own ride)
        const newRiderDirectFare = baseFare + ((newRiderDistance / 1000) * perKmRate);

        // Additional distance cost
        const additionalDistance = newRouteDistance - originalDistance;
        const additionalDistanceCost = (additionalDistance / 1000) * perKmRate;

        // Calculate fare split based on distance ratio
        const originalRiderNewFare = originalFare * 0.7; // 30% discount for original rider
        const newRiderFare = baseFare * 0.5 + ((newRiderDistance / 1000) * perKmRate * 0.8); // 20% discount on distance

        // Calculate savings
        const originalRiderSavings = originalFare - originalRiderNewFare;
        const newRiderSavings = newRiderDirectFare - newRiderFare;
        const totalSavings = originalRiderSavings + newRiderSavings;

        // Add additional information to the prediction
        const enhancedPrediction = {
          ...prediction,
          originalDistance,
          newRouteDistance,
          newRiderDistance,
          additionalDistance,
          additionalTimeEstimate: Math.round((additionalDistance / 1000) / 30 * 60 * 60), // Assuming 30 km/h average speed, convert to seconds
          fareDetails: {
            originalRider: {
              originalFare: Math.round(originalFare),
              newFare: Math.round(originalRiderNewFare),
              savings: Math.round(originalRiderSavings)
            },
            newRider: {
              directFare: Math.round(newRiderDirectFare),
              sharedFare: Math.round(newRiderFare),
              savings: Math.round(newRiderSavings)
            },
            totalSavings: Math.round(totalSavings)
          },
          environmentalImpact: {
            co2Reduction: Math.round((newRiderDistance / 1000) * 0.12 * 1000) / 1000, // kg of CO2 saved (0.12 kg per km)
            fuelSaved: Math.round((newRiderDistance / 1000) * 0.08 * 100) / 100 // liters of fuel saved (0.08L per km)
          }
        };

        // Store the sharing request in the ride object
        if (!rideRequests[rideId].sharingRequests) {
          rideRequests[rideId].sharingRequests = [];
        }

        const sharingRequestId = `share_${Date.now()}`;

        rideRequests[rideId].sharingRequests.push({
          id: sharingRequestId,
          newRiderId,
          newPickupLocation,
          newDestination,
          prediction: enhancedPrediction,
          status: 'pending',
          timestamp: new Date()
        });

        // Notify the driver about the ride sharing request with prediction
        io.to(driverStatus[rideRequests[rideId].driverId].socketId).emit('ride:sharing_request', {
          rideId,
          sharingRequestId,
          newRiderId,
          newPickupLocation,
          newDestination,
          prediction: enhancedPrediction
        });

        // Acknowledge the request
        socket.emit('ride:sharing_requested', {
          success: true,
          message: 'Ride sharing request sent to driver',
          sharingRequestId,
          prediction: enhancedPrediction
        });

        // Make the sharing request visible to other potential riders
        // This allows the system to show active ride sharing opportunities
        Object.keys(driverStatus).forEach(otherDriverId => {
          if (driverStatus[otherDriverId].available && otherDriverId !== rideRequests[rideId].driverId) {
            io.to(driverStatus[otherDriverId].socketId).emit('ride:sharing_opportunity', {
              rideId,
              sharingRequestId,
              prediction: enhancedPrediction
            });
          }
        });

        console.log(`Ride sharing request ${sharingRequestId} processed successfully`);
      } catch (error) {
        console.error('Error processing ride sharing request:', error);
        socket.emit('ride:sharing_requested', {
          success: false,
          message: 'Failed to process ride sharing request: ' + error.message
        });
      }
    } else {
      socket.emit('ride:sharing_requested', {
        success: false,
        message: 'No active ride found with the provided ID'
      });
    }
  });

  // Driver confirms a ride sharing request after analysis
  socket.on('ride:confirm_sharing', async (data) => {
    const { rideId, driverId, confirmed } = data;

    if (!rideRequests[rideId]) {
      console.log(`Error: Ride ${rideId} not found for sharing confirmation`);
      socket.emit('ride:error', {
        rideId,
        error: 'ride_not_found',
        message: `الرحلة رقم ${rideId} غير موجودة أو تم إلغاؤها`
      });
      return;
    }

    // If driver confirms sharing despite the detour
    if (confirmed) {
      // Find the current ride
      const currentRideId = Object.values(rideRequests).find(
        ride => ride.driverId === driverId &&
        (ride.status === 'accepted' || ride.status === 'in_progress') &&
        ride.id !== rideId
      )?.id;

      if (currentRideId) {
        try {
          // Update ride status
          rideRequests[rideId].status = 'accepted';
          rideRequests[rideId].driverId = driverId;
          rideRequests[rideId].acceptedAt = new Date();

          // Add the new ride as stops to the current ride
          await rideService.addStopToRide(currentRideId, {
            riderId: rideRequests[rideId].riderId,
            pickupLocation: rideRequests[rideId].pickupLocation,
            destination: rideRequests[rideId].destination
          });

          // Mark this ride as merged
          rideRequests[rideId].mergedWithRideId = currentRideId;
          rideRequests[rideId].status = 'merged';

          // Get driver info
          let driverInfo;
          if (driverStatus[driverId] && driverStatus[driverId].info) {
            driverInfo = driverStatus[driverId].info;
          } else {
            driverInfo = {
              name: `Driver ${driverId.substring(0, 5)}`,
              rating: (Math.random() * 2 + 3).toFixed(1),
              car: 'Toyota Corolla',
              plate: `ABC ${Math.floor(Math.random() * 1000)}`,
              eta: `${Math.floor(Math.random() * 10) + 2} minutes`,
            };
          }

          // Notify the rider about the shared ride
          io.to(rideRequests[rideId].riderSocketId).emit('ride:shared', {
            rideId,
            mergedWithRideId: currentRideId,
            driverId,
            timestamp: new Date().toISOString(),
            message: 'Your ride will be shared with another passenger',
            driverInfo: driverInfo,
            driverLocation: {
              lat: rideRequests[rideId].pickupLocation.lat - (Math.random() * 0.01),
              lng: rideRequests[rideId].pickupLocation.lng - (Math.random() * 0.01),
            }
          });

          logger.info(`Ride ${rideId} merged with ride ${currentRideId} after driver confirmation`);
        } catch (error) {
          logger.error(`Error merging rides after confirmation: ${error.message}`);
          socket.emit('ride:error', {
            rideId,
            error: 'merge_failed',
            message: `Failed to merge rides: ${error.message}`
          });
        }
      } else {
        logger.error(`No active ride found for driver ${driverId} to merge with ride ${rideId}`);
        socket.emit('ride:error', {
          rideId,
          error: 'no_active_ride',
          message: 'No active ride found to merge with'
        });
      }
    } else {
      // Driver declined to share the ride
      logger.info(`Driver ${driverId} declined to share ride ${rideId}`);

      // Notify the rider
      io.to(rideRequests[rideId].riderSocketId).emit('ride:driver_rejected', {
        rideId,
        driverId,
        timestamp: new Date().toISOString(),
        message: 'Driver cannot accommodate your ride at this time'
      });

      // Look for another driver
      const availableDrivers = Object.keys(driverStatus).filter(id =>
        driverStatus[id].available &&
        driverStatus[id].socketId &&
        id !== driverId
      );

      if (availableDrivers.length > 0) {
        // Choose a random driver
        const randomIndex = Math.floor(Math.random() * availableDrivers.length);
        const newDriverId = availableDrivers[randomIndex];

        // Send ride request to the new driver
        io.to(driverStatus[newDriverId].socketId).emit('ride:new_request', rideRequests[rideId]);
        logger.info(`Ride request ${rideId} forwarded to another driver: ${newDriverId}`);
      } else {
        logger.info(`No available drivers for ride ${rideId} after sharing rejection`);
        // Inform the rider that no drivers are available
        io.to(rideRequests[rideId].riderSocketId).emit('ride:no_drivers', {
          rideId,
          timestamp: new Date().toISOString(),
          message: 'No available drivers at the moment. Please try again later.'
        });
      }
    }
  });

  // Driver responds to ride sharing request
  socket.on('ride:sharing_response', (data) => {
    const { rideId, sharingRequestId, accepted, driverId } = data;

    console.log(`Driver ${driverId} ${accepted ? 'accepted' : 'rejected'} sharing request ${sharingRequestId} for ride ${rideId}`);

    if (rideRequests[rideId] && rideRequests[rideId].sharingRequests) {
      // Find the specific sharing request
      const sharingRequest = rideRequests[rideId].sharingRequests.find(
        req => req.id === sharingRequestId
      );

      if (!sharingRequest) {
        console.error(`Sharing request ${sharingRequestId} not found for ride ${rideId}`);
        return;
      }

      // Update the sharing request status
      sharingRequest.status = accepted ? 'accepted' : 'rejected';
      sharingRequest.responseTime = new Date();

      const newRiderId = sharingRequest.newRiderId;
      const newRiderSocketId = sharingRequest.newRiderSocketId;

      if (accepted) {
        // Update ride request to include the new rider
        if (!rideRequests[rideId].sharedRiders) {
          rideRequests[rideId].sharedRiders = [];
        }

        // Add the new rider to the shared riders list
        rideRequests[rideId].sharedRiders.push({
          riderId: newRiderId,
          pickupLocation: sharingRequest.newPickupLocation,
          destination: sharingRequest.newDestination,
          status: 'accepted',
          joinedAt: new Date()
        });

        console.log(`Added rider ${newRiderId} to shared riders for ride ${rideId}`);

        // Notify the new rider
        if (newRiderSocketId) {
          io.to(newRiderSocketId).emit('ride:sharing_accepted', {
            rideId,
            sharingRequestId,
            driverId,
            prediction: sharingRequest.prediction,
            estimatedPickupTime: 5 * 60 // 5 minutes in seconds
          });
        }

        // Notify the original rider
        io.to(rideRequests[rideId].riderSocketId).emit('ride:sharing_added', {
          rideId,
          sharingRequestId,
          newRiderId,
          prediction: sharingRequest.prediction
        });

        // Update the ride's route to include the new pickup and destination
        // In a real app, you would recalculate the route here

        // Log the acceptance for analytics
        console.log(`Ride sharing accepted for ride ${rideId}, new rider: ${newRiderId}, driver: ${driverId}`);

        // Notify other potential riders that this sharing opportunity is no longer available
        Object.keys(driverStatus).forEach(otherDriverId => {
          if (driverStatus[otherDriverId].available && otherDriverId !== driverId) {
            io.to(driverStatus[otherDriverId].socketId).emit('ride:sharing_opportunity_taken', {
              rideId,
              sharingRequestId
            });
          }
        });
      } else {
        // Notify the new rider that the request was rejected
        if (newRiderSocketId) {
          io.to(newRiderSocketId).emit('ride:sharing_rejected', {
            rideId,
            sharingRequestId,
            driverId,
            reason: data.reason || 'Driver declined the request'
          });
        }

        // Log the rejection for analytics
        console.log(`Ride sharing rejected for ride ${rideId}, new rider: ${newRiderId}, driver: ${driverId}, reason: ${data.reason || 'Not provided'}`);
      }
    } else {
      console.error(`Ride ${rideId} not found or has no sharing requests`);
    }
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);

    // Check if it was a driver and update status
    Object.keys(driverStatus).forEach(driverId => {
      if (driverStatus[driverId].socketId === socket.id) {
        console.log(`Driver ${driverId} disconnected`);
        delete driverStatus[driverId];
      }
    });
  });
});

// Helper function to calculate route distance
async function calculateRouteDistance(...waypoints) {
  try {
    // Format waypoints for OSRM API
    const waypointsStr = waypoints
      .map(point => `${point.lng},${point.lat}`)
      .join(';');

    const url = `https://router.project-osrm.org/route/v1/driving/${waypointsStr}?overview=false`;
    const response = await axios.get(url);

    if (response.data.code !== 'Ok' || response.data.routes.length === 0) {
      throw new Error('Failed to calculate route');
    }

    return response.data.routes[0].distance; // Distance in meters
  } catch (error) {
    console.error('Error calculating route distance:', error);
    throw error;
  }
}

// الصفحة الرئيسية
app.get('/', (req, res) => {
  res.sendFile(__dirname + '/public/index.html');
});

// واجهة برمجة للحصول على مسار بين نقطتين
app.get('/api/route', async (req, res) => {
  try {
    const { startLat, startLng, endLat, endLng } = req.query;

    // التحقق من وجود الإحداثيات
    if (!startLat || !startLng || !endLat || !endLng) {
      return res.status(400).json({
        success: false,
        message: 'يجب توفير إحداثيات البداية والنهاية'
      });
    }

    // استعلام من خدمة OSRM
    const url = `https://router.project-osrm.org/route/v1/driving/` +
                `${startLng},${startLat};${endLng},${endLat}` +
                `?overview=full&geometries=polyline`;

    console.log(`Requesting route from OSRM: ${url}`);

    const response = await axios.get(url);

    if (response.data.code !== 'Ok') {
      throw new Error(`OSRM API error: ${response.data.code} - ${response.data.message || 'No error message'}`);
    }

    if (response.data.routes.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على مسار'
      });
    }

    // استخراج بيانات المسار
    const route = response.data.routes[0];
    const encodedPolyline = route.geometry;
    const decodedPolyline = polyline.decode(encodedPolyline);

    // تحويل النقاط إلى تنسيق [lat, lng]
    const points = decodedPolyline.map(point => ({
      lat: point[0],
      lng: point[1]
    }));

    // إعداد البيانات للرد
    const routeData = {
      success: true,
      distance: route.distance, // المسافة بالمتر
      duration: route.duration, // المدة بالثانية
      points: points,
      encoded_polyline: encodedPolyline
    };

    res.json(routeData);
  } catch (error) {
    console.error('Error fetching route:', error);
    res.status(500).json({
      success: false,
      message: `فشل في الحصول على المسار: ${error.message}`
    });
  }
});

// API pour créer une nouvelle demande de trajet
app.post('/api/rides', async (req, res) => {
  try {
    const { riderId, pickupLocation, destination } = req.body;

    // Vérifier les données requises
    if (!riderId || !pickupLocation || !destination) {
      return res.status(400).json({
        success: false,
        message: 'Données manquantes: riderId, pickupLocation et destination sont requis'
      });
    }

    // Validate location data
    if (!pickupLocation.lat || !pickupLocation.lng || !destination.lat || !destination.lng) {
      return res.status(400).json({
        success: false,
        message: 'Coordonnées de localisation invalides'
      });
    }

    // Créer la demande de trajet dans la base de données
    const ride = await dbService.createRide({
      riderId,
      pickupLocation,
      destination,
      status: 'pending',
      timestamp: new Date()
    });

    logger.info(`Nouvelle demande de trajet créée via API: ${ride.id}`);

    // Notifier tous les conducteurs disponibles via Socket.IO
    Object.keys(driverStatus).forEach(driverId => {
      if (driverStatus[driverId].available && driverStatus[driverId].socketId) {
        io.to(driverStatus[driverId].socketId).emit('ride:new_request', {
          id: ride.id,
          riderId: ride.riderId,
          pickupLocation: ride.pickupLocation,
          destination: ride.destination,
          status: ride.status,
          timestamp: ride.createdAt || ride.timestamp
        });
      }
    });

    res.status(201).json({
      success: true,
      rideId: ride.id,
      ride: {
        id: ride.id,
        riderId: ride.riderId,
        pickupLocation: ride.pickupLocation,
        destination: ride.destination,
        status: ride.status,
        createdAt: ride.createdAt || ride.timestamp
      },
      message: 'Demande de trajet créée avec succès'
    });
  } catch (error) {
    logger.error('Erreur lors de la création de la demande de trajet:', error);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la création de la demande de trajet: ${error.message}`
    });
  }
});

// API pour récupérer les demandes de trajet
app.get('/api/rides', (req, res) => {
  try {
    const { status, driverId, riderId } = req.query;
    const filters = {};

    if (status) filters.status = status;
    if (driverId) filters.driverId = driverId;
    if (riderId) filters.riderId = riderId;

    const rides = dbService.getRides(filters);

    res.json({
      success: true,
      rides
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des demandes de trajet:', error);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la récupération des demandes de trajet: ${error.message}`
    });
  }
});

// API to add a stop to an existing ride
app.post('/api/rides/:rideId/stops', async (req, res) => {
  try {
    const { rideId } = req.params;
    const { riderId, pickupLocation, destination } = req.body;

    // Validate required data
    if (!riderId || !pickupLocation || !destination) {
      return res.status(400).json({
        success: false,
        message: 'Missing required data: riderId, pickupLocation, and destination are required'
      });
    }

    // Add stop to the ride
    const updatedRide = await rideService.addStopToRide(rideId, {
      riderId,
      pickupLocation,
      destination
    });

    // Notify the driver about the new stop
    if (updatedRide.driverId) {
      const driverSocketId = driverStatus[updatedRide.driverId]?.socketId;
      if (driverSocketId) {
        io.to(driverSocketId).emit('ride:stop_added', {
          rideId: updatedRide._id,
          stops: updatedRide.stops,
          route: updatedRide.route
        });
      }
    }

    res.json({
      success: true,
      ride: updatedRide
    });
  } catch (error) {
    logger.error(`Error adding stop to ride: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error adding stop to ride: ${error.message}`
    });
  }
});

// API to analyze if a driver can accept a new ride while on an existing ride
app.post('/api/rides/analyze-sharing', async (req, res) => {
  try {
    const { driverId, riderId, pickupLocation, destination } = req.body;

    // Validate required data
    if (!driverId || !riderId || !pickupLocation || !destination) {
      return res.status(400).json({
        success: false,
        message: 'Missing required data: driverId, riderId, pickupLocation, and destination are required'
      });
    }

    // Analyze ride sharing possibility
    const analysis = await rideService.analyzeRideSharing(driverId, {
      riderId,
      pickupLocation,
      destination
    });

    res.json({
      success: true,
      analysis
    });
  } catch (error) {
    logger.error(`Error analyzing ride sharing: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error analyzing ride sharing: ${error.message}`
    });
  }
});

// API pour récupérer une demande de trajet par son ID
app.get('/api/rides/:rideId', async (req, res) => {
  try {
    const { rideId } = req.params;
    const ride = await dbService.getRideById(rideId);

    if (!ride) {
      return res.status(404).json({
        success: false,
        message: `Demande de trajet non trouvée: ${rideId}`
      });
    }

    res.json({
      success: true,
      ride
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération de la demande de trajet:', error);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la récupération de la demande de trajet: ${error.message}`
    });
  }
});

// API pour mettre à jour une demande de trajet
app.put('/api/rides/:rideId', (req, res) => {
  try {
    const { rideId } = req.params;
    const updates = req.body;

    // Vérifier si la demande de trajet existe
    const existingRide = dbService.getRideById(rideId);
    if (!existingRide) {
      return res.status(404).json({
        success: false,
        message: `Demande de trajet non trouvée: ${rideId}`
      });
    }

    // Mettre à jour la demande de trajet
    const updatedRide = dbService.updateRide(rideId, updates);

    // Si le statut a changé, émettre l'événement correspondant via Socket.IO
    if (updates.status && updates.status !== existingRide.status) {
      if (updates.status === 'accepted' && updates.driverId) {
        io.to(existingRide.riderSocketId).emit('ride:accepted', {
          rideId,
          driverId: updates.driverId
        });
      } else if (updates.status === 'in_progress') {
        io.to(existingRide.riderSocketId).emit('ride:started', { rideId });
      } else if (updates.status === 'completed') {
        io.to(existingRide.riderSocketId).emit('ride:completed', { rideId });
      }
    }

    res.json({
      success: true,
      ride: updatedRide
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la demande de trajet:', error);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la mise à jour de la demande de trajet: ${error.message}`
    });
  }
});

// API pour enregistrer un nouveau conducteur
app.post('/api/drivers', (req, res) => {
  try {
    const { name, vehicleType, vehicleNumber, latitude, longitude } = req.body;

    // Vérifier les données requises
    if (!name || !vehicleType || !vehicleNumber) {
      return res.status(400).json({
        success: false,
        message: 'Données manquantes: name, vehicleType et vehicleNumber sont requis'
      });
    }

    // Créer le conducteur dans la base de données
    const driver = dbService.createDriver({
      name,
      vehicleType,
      vehicleNumber,
      rating: 5.0, // Note par défaut
      totalTrips: 0,
      isAvailable: true,
      latitude: latitude || 0,
      longitude: longitude || 0
    });

    res.status(201).json({
      success: true,
      driverId: driver.id,
      message: 'Conducteur créé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la création du conducteur:', error);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la création du conducteur: ${error.message}`
    });
  }
});

// API pour récupérer les conducteurs disponibles
app.get('/api/drivers', async (req, res) => {
  try {
    const { isAvailable } = req.query;
    const filters = {};

    if (isAvailable !== undefined) {
      filters.isAvailable = isAvailable === 'true';
    }

    const drivers = await dbService.getDrivers(filters);

    res.json({
      success: true,
      drivers
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des conducteurs:', error);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la récupération des conducteurs: ${error.message}`
    });
  }
});

// API pour générer des conducteurs fictifs
app.post('/api/mock-drivers', async (req, res) => {
  try {
    const { count = 10, center, radiusKm = 10 } = req.body;

    // Validate input
    if (count < 1 || count > 100) {
      return res.status(400).json({
        success: false,
        message: 'Le nombre de conducteurs doit être compris entre 1 et 100'
      });
    }

    // Generate mock drivers
    const options = {
      center: center || { lat: 24.7136, lng: 46.6753 }, // Default: Riyadh
      radiusKm: radiusKm
    };

    const mockDrivers = driverGeneratorService.generateDrivers(count, options);

    // Save drivers to database
    const savedDrivers = [];
    for (const driver of mockDrivers) {
      const savedDriver = await dbService.createDriver(driver);
      savedDrivers.push(savedDriver);
    }

    logger.info(`Generated and saved ${savedDrivers.length} mock drivers`);

    res.status(201).json({
      success: true,
      count: savedDrivers.length,
      drivers: savedDrivers
    });
  } catch (error) {
    logger.error(`Error generating mock drivers: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la génération des conducteurs fictifs: ${error.message}`
    });
  }
});

// API pour récupérer les conducteurs proches d'une position
app.get('/api/nearby-drivers', async (req, res) => {
  try {
    const { lat, lng, radius = 5, limit = 10 } = req.query;

    // Validate input
    if (!lat || !lng) {
      return res.status(400).json({
        success: false,
        message: 'Les paramètres lat et lng sont requis'
      });
    }

    const center = {
      lat: parseFloat(lat),
      lng: parseFloat(lng)
    };

    const radiusKm = parseFloat(radius);
    const limitCount = parseInt(limit);

    // Get nearby drivers
    const drivers = await dbService.getDriversByLocation(center, radiusKm, limitCount);

    res.json({
      success: true,
      count: drivers.length,
      drivers
    });
  } catch (error) {
    logger.error(`Error getting nearby drivers: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la récupération des conducteurs proches: ${error.message}`
    });
  }
});

// تشغيل الخادم
server.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
