# C/C++ build system timings
generate_cxx_metadata
  [gap of 48ms]
  create-invalidation-state 27ms
  execute-generate-process
    exec-configure 5117ms
    [gap of 55ms]
  execute-generate-process completed in 5181ms
  [gap of 186ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 5487ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 69ms]
  create-invalidation-state 301ms
  [gap of 110ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 503ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 47ms]
  create-invalidation-state 162ms
  [gap of 48ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 274ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 42ms]
  create-invalidation-state 181ms
  [gap of 107ms]
  write-metadata-json-to-file 112ms
  [gap of 12ms]
generate_cxx_metadata completed in 454ms

