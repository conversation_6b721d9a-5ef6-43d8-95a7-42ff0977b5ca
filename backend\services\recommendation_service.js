/**
 * Recommendation Service
 * 
 * This service implements intelligent recommendation features including:
 * - Driver-rider matching based on preferences and ratings
 * - Best rider recommendations for drivers
 * - Best driver recommendations for riders
 * - Learning from user interactions and feedback
 */

const logger = require('../utils/logger');
const dbService = require('./database_service');

class RecommendationService {
  constructor() {
    this.userPreferences = new Map(); // Store user preferences
    this.interactionHistory = new Map(); // Store interaction history
    this.ratingHistory = new Map(); // Store rating history
    
    logger.info('Recommendation Service initialized');
  }

  /**
   * Recommend best riders for a driver
   * @param {string} driverId - ID of the driver
   * @param {Array} availableRiders - Array of available riders
   * @param {Object} options - Recommendation options
   * @returns {Promise<Array>} - Array of recommended riders
   */
  async recommendBestRiders(driverId, availableRiders, options = {}) {
    try {
      const driver = await dbService.getDriverById(driverId);
      if (!driver) {
        throw new Error(`Driver ${driverId} not found`);
      }

      const driverPreferences = this.getDriverPreferences(driverId);
      const driverHistory = this.getDriverHistory(driverId);
      
      const recommendations = [];
      
      for (const rider of availableRiders) {
        const score = await this.calculateRiderScore(driver, rider, driverPreferences, driverHistory);
        
        if (score.overall > 0.5) { // Only recommend riders with score > 0.5
          recommendations.push({
            rider,
            score: score.overall,
            reasons: score.reasons,
            compatibility: score.compatibility,
            estimatedEarnings: this.estimateEarnings(driver, rider),
            riskLevel: score.riskLevel
          });
        }
      }
      
      // Sort by score (highest first)
      recommendations.sort((a, b) => b.score - a.score);
      
      return recommendations.slice(0, options.limit || 10);
    } catch (error) {
      logger.error(`Error recommending best riders: ${error.message}`);
      throw error;
    }
  }

  /**
   * Recommend best drivers for a rider
   * @param {string} riderId - ID of the rider
   * @param {Array} availableDrivers - Array of available drivers
   * @param {Object} options - Recommendation options
   * @returns {Promise<Array>} - Array of recommended drivers
   */
  async recommendBestDrivers(riderId, availableDrivers, options = {}) {
    try {
      const rider = await this.getRiderById(riderId);
      if (!rider) {
        throw new Error(`Rider ${riderId} not found`);
      }

      const riderPreferences = this.getRiderPreferences(riderId);
      const riderHistory = this.getRiderHistory(riderId);
      
      const recommendations = [];
      
      for (const driver of availableDrivers) {
        const score = await this.calculateDriverScore(rider, driver, riderPreferences, riderHistory);
        
        if (score.overall > 0.5) { // Only recommend drivers with score > 0.5
          recommendations.push({
            driver,
            score: score.overall,
            reasons: score.reasons,
            compatibility: score.compatibility,
            estimatedArrival: this.estimateArrivalTime(driver, rider),
            safetyScore: score.safetyScore
          });
        }
      }
      
      // Sort by score (highest first)
      recommendations.sort((a, b) => b.score - a.score);
      
      return recommendations.slice(0, options.limit || 10);
    } catch (error) {
      logger.error(`Error recommending best drivers: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calculate rider score for a driver
   * @param {Object} driver - Driver object
   * @param {Object} rider - Rider object
   * @param {Object} driverPreferences - Driver preferences
   * @param {Object} driverHistory - Driver history
   * @returns {Promise<Object>} - Score breakdown
   */
  async calculateRiderScore(driver, rider, driverPreferences, driverHistory) {
    let score = 0.5; // Base score
    const reasons = [];
    
    // Rating-based scoring (30% weight)
    if (rider.rating) {
      const ratingScore = Math.min(rider.rating / 5.0, 1.0);
      score += ratingScore * 0.3;
      if (rider.rating >= 4.5) {
        reasons.push('High rider rating');
      }
    }
    
    // Distance-based scoring (20% weight)
    const distance = this.calculateDistance(driver.location, rider.pickupLocation);
    const distanceScore = Math.max(0, 1 - (distance / 10)); // Penalize distances > 10km
    score += distanceScore * 0.2;
    if (distance < 2) {
      reasons.push('Very close pickup location');
    }
    
    // Trip value scoring (25% weight)
    const tripDistance = this.calculateDistance(rider.pickupLocation, rider.destination);
    const tripValueScore = Math.min(tripDistance / 20, 1.0); // Favor longer trips up to 20km
    score += tripValueScore * 0.25;
    if (tripDistance > 10) {
      reasons.push('Long-distance trip (higher earnings)');
    }
    
    // Preference matching (15% weight)
    const preferenceScore = this.matchRiderPreferences(driverPreferences, rider);
    score += preferenceScore * 0.15;
    if (preferenceScore > 0.8) {
      reasons.push('Excellent preference match');
    }
    
    // Historical compatibility (10% weight)
    const historyScore = this.calculateHistoricalCompatibility(driverHistory, rider);
    score += historyScore * 0.1;
    if (historyScore > 0.8) {
      reasons.push('Good historical compatibility');
    }
    
    // Safety and reliability factors
    const riskLevel = this.assessRiderRisk(rider, driverHistory);
    const safetyScore = 1 - riskLevel;
    
    return {
      overall: Math.min(1.0, score),
      compatibility: preferenceScore,
      riskLevel,
      safetyScore,
      reasons
    };
  }

  /**
   * Calculate driver score for a rider
   * @param {Object} rider - Rider object
   * @param {Object} driver - Driver object
   * @param {Object} riderPreferences - Rider preferences
   * @param {Object} riderHistory - Rider history
   * @returns {Promise<Object>} - Score breakdown
   */
  async calculateDriverScore(rider, driver, riderPreferences, riderHistory) {
    let score = 0.5; // Base score
    const reasons = [];
    
    // Rating-based scoring (35% weight)
    if (driver.rating) {
      const ratingScore = Math.min(driver.rating / 5.0, 1.0);
      score += ratingScore * 0.35;
      if (driver.rating >= 4.7) {
        reasons.push('Excellent driver rating');
      }
    }
    
    // Distance and ETA scoring (25% weight)
    const distance = this.calculateDistance(driver.location, rider.pickupLocation);
    const etaScore = Math.max(0, 1 - (distance / 15)); // Penalize distances > 15km
    score += etaScore * 0.25;
    if (distance < 3) {
      reasons.push('Quick pickup time');
    }
    
    // Vehicle quality scoring (15% weight)
    const vehicleScore = this.assessVehicleQuality(driver.vehicle);
    score += vehicleScore * 0.15;
    if (vehicleScore > 0.8) {
      reasons.push('High-quality vehicle');
    }
    
    // Preference matching (15% weight)
    const preferenceScore = this.matchDriverPreferences(riderPreferences, driver);
    score += preferenceScore * 0.15;
    if (preferenceScore > 0.8) {
      reasons.push('Perfect preference match');
    }
    
    // Experience and reliability (10% weight)
    const experienceScore = this.assessDriverExperience(driver);
    score += experienceScore * 0.1;
    if (driver.totalTrips > 1000) {
      reasons.push('Highly experienced driver');
    }
    
    // Safety scoring
    const safetyScore = this.assessDriverSafety(driver);
    
    return {
      overall: Math.min(1.0, score),
      compatibility: preferenceScore,
      safetyScore,
      reasons
    };
  }

  /**
   * Learn from user interactions and feedback
   * @param {string} userId - ID of the user
   * @param {string} userType - Type of user ('driver' or 'rider')
   * @param {Object} interaction - Interaction data
   */
  learnFromInteraction(userId, userType, interaction) {
    try {
      const key = `${userType}_${userId}`;
      
      if (!this.interactionHistory.has(key)) {
        this.interactionHistory.set(key, []);
      }
      
      this.interactionHistory.get(key).push({
        ...interaction,
        timestamp: new Date()
      });
      
      // Update preferences based on interaction
      this.updatePreferencesFromInteraction(userId, userType, interaction);
      
      logger.info(`Learned from interaction for ${userType} ${userId}`);
    } catch (error) {
      logger.error(`Error learning from interaction: ${error.message}`);
    }
  }

  /**
   * Update user preferences based on their interactions
   * @param {string} userId - ID of the user
   * @param {string} userType - Type of user ('driver' or 'rider')
   * @param {Object} interaction - Interaction data
   */
  updatePreferencesFromInteraction(userId, userType, interaction) {
    const key = `${userType}_${userId}`;
    
    if (!this.userPreferences.has(key)) {
      this.userPreferences.set(key, {
        preferredRating: 4.0,
        preferredDistance: 5.0,
        preferredTripLength: 10.0,
        genderPreference: null,
        vehiclePreference: null,
        musicPreference: null,
        smokingTolerance: false
      });
    }
    
    const preferences = this.userPreferences.get(key);
    
    // Update preferences based on positive interactions
    if (interaction.rating >= 4) {
      if (interaction.partnerRating) {
        preferences.preferredRating = (preferences.preferredRating + interaction.partnerRating) / 2;
      }
      
      if (interaction.distance) {
        preferences.preferredDistance = (preferences.preferredDistance + interaction.distance) / 2;
      }
      
      if (interaction.tripLength) {
        preferences.preferredTripLength = (preferences.preferredTripLength + interaction.tripLength) / 2;
      }
    }
    
    this.userPreferences.set(key, preferences);
  }

  // Helper methods
  getDriverPreferences(driverId) {
    return this.userPreferences.get(`driver_${driverId}`) || {};
  }

  getRiderPreferences(riderId) {
    return this.userPreferences.get(`rider_${riderId}`) || {};
  }

  getDriverHistory(driverId) {
    return this.interactionHistory.get(`driver_${driverId}`) || [];
  }

  getRiderHistory(riderId) {
    return this.interactionHistory.get(`rider_${riderId}`) || [];
  }

  calculateDistance(point1, point2) {
    const R = 6371; // Earth's radius in km
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLon = (point2.lng - point1.lng) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  // Additional helper methods will be implemented as needed
}

module.exports = new RecommendationService();
