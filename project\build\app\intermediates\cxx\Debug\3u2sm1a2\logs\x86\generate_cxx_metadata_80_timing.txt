# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 45ms
  [gap of 40ms]
generate_cxx_metadata completed in 109ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 135ms]
  create-invalidation-state 34ms
  [gap of 34ms]
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 219ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 26ms]
  create-invalidation-state 34ms
  [gap of 21ms]
generate_cxx_metadata completed in 81ms

