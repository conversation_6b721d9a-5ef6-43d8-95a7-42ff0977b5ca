import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:project/core/config/api_config.dart';
import 'package:project/core/services/socket_service.dart';

/// Enhanced Backend Communication Service
/// Handles all communication between the app and backend for ride management
class EnhancedBackendCommunication {
  static final EnhancedBackendCommunication _instance =
      EnhancedBackendCommunication._internal();
  static EnhancedBackendCommunication get instance => _instance;
  EnhancedBackendCommunication._internal();

  // Communication state
  bool _isConnected = false;
  Timer? _connectionMonitor;
  int _retryAttempts = 0;
  static const int _maxRetryAttempts = 5;
  static const Duration _connectionCheckInterval = Duration(seconds: 30);

  // Event streams for real-time updates
  final StreamController<Map<String, dynamic>> _rideUpdatesController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _driverLocationController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _trafficUpdatesController =
      StreamController.broadcast();
  final StreamController<String> _connectionStatusController =
      StreamController.broadcast();

  // Getters for streams
  Stream<Map<String, dynamic>> get rideUpdates => _rideUpdatesController.stream;
  Stream<Map<String, dynamic>> get driverLocationUpdates =>
      _driverLocationController.stream;
  Stream<Map<String, dynamic>> get trafficUpdates =>
      _trafficUpdatesController.stream;
  Stream<String> get connectionStatus => _connectionStatusController.stream;

  /// Initialize the communication service
  void initialize() {
    _setupSocketConnection();
    _startConnectionMonitoring();
    debugPrint('Enhanced Backend Communication initialized');
  }

  /// Setup socket connection with enhanced error handling
  void _setupSocketConnection() {
    SocketService.instance.initialize();
    SocketService.instance.connect();

    // Enhanced socket event listeners
    _setupEnhancedSocketListeners();
  }

  /// Setup enhanced socket listeners using SocketService
  void _setupEnhancedSocketListeners() {
    // Monitor connection status through SocketService
    _monitorConnectionStatus();

    // Enhanced ride event listeners
    SocketService.instance.listenTo('ride:status_update', (data) {
      _rideUpdatesController.add({
        'type': 'status_update',
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    SocketService.instance.listenTo('ride:driver_assigned', (data) {
      _rideUpdatesController.add({
        'type': 'driver_assigned',
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    SocketService.instance.listenTo('ride:accepted', (data) {
      _rideUpdatesController.add({
        'type': 'driver_assigned',
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    SocketService.instance.listenTo('ride:route_optimized', (data) {
      _rideUpdatesController.add({
        'type': 'route_optimized',
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    // Enhanced driver location updates
    SocketService.instance.listenTo('driver:location_update', (data) {
      _driverLocationController.add({
        'lat': data['lat'],
        'lng': data['lng'],
        'speed': data['speed'] ?? 0.0,
        'bearing': data['bearing'] ?? 0.0,
        'accuracy': data['accuracy'] ?? 0.0,
        'timestamp': data['timestamp'] ?? DateTime.now().toIso8601String(),
        'rideId': data['rideId'],
      });
    });

    // Traffic and road condition updates
    SocketService.instance.listenTo('traffic:update', (data) {
      _trafficUpdatesController.add({
        'level': data['level'] ?? 'normal',
        'delay': data['delay'] ?? 0.0,
        'affectedRoute': data['affectedRoute'],
        'alternativeRoute': data['alternativeRoute'],
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    // Real-time ETA updates
    SocketService.instance.listenTo('ride:eta_update', (data) {
      _rideUpdatesController.add({
        'type': 'eta_update',
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    // Listen to existing SocketService events
    SocketService.instance.onRideAccepted.listen((data) {
      _rideUpdatesController.add({
        'type': 'ride_accepted',
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    SocketService.instance.onRideStarted.listen((rideId) {
      _rideUpdatesController.add({
        'type': 'ride_started',
        'data': {'rideId': rideId},
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    SocketService.instance.onRideCompleted.listen((rideId) {
      _rideUpdatesController.add({
        'type': 'ride_completed',
        'data': {'rideId': rideId},
        'timestamp': DateTime.now().toIso8601String(),
      });
    });
  }

  /// Monitor connection status through periodic checks
  void _monitorConnectionStatus() {
    Timer.periodic(const Duration(seconds: 5), (timer) {
      // Check if socket exists and is connected
      final currentStatus = SocketService.instance.socket?.connected ?? false;
      if (currentStatus != _isConnected) {
        _isConnected = currentStatus;
        if (_isConnected) {
          _retryAttempts = 0;
          _connectionStatusController.add('connected');
          debugPrint('Backend connection established');
        } else {
          _connectionStatusController.add('disconnected');
          _handleConnectionLoss();
          debugPrint('Backend connection lost');
        }
      }
    });
  }

  /// Start connection monitoring
  void _startConnectionMonitoring() {
    _connectionMonitor?.cancel();
    _connectionMonitor = Timer.periodic(_connectionCheckInterval, (timer) {
      _checkConnectionHealth();
    });
  }

  /// Check connection health
  void _checkConnectionHealth() {
    if (!_isConnected) {
      _attemptReconnection();
    } else {
      // Send heartbeat to verify connection
      sendHeartbeat();
    }
  }

  /// Handle connection loss
  void _handleConnectionLoss() {
    if (_retryAttempts < _maxRetryAttempts) {
      _attemptReconnection();
    } else {
      _connectionStatusController.add('failed');
      debugPrint('Max retry attempts reached. Connection failed.');
    }
  }

  /// Attempt to reconnect
  void _attemptReconnection() {
    if (_retryAttempts < _maxRetryAttempts) {
      _retryAttempts++;
      debugPrint('Attempting reconnection $_retryAttempts/$_maxRetryAttempts');
      SocketService.instance.connect();
    }
  }

  /// Send heartbeat to maintain connection
  void sendHeartbeat() {
    if (_isConnected) {
      SocketService.instance.socket?.emit('heartbeat', {
        'timestamp': DateTime.now().toIso8601String(),
        'clientType': 'rider_app',
      });
    }
  }

  /// Request a ride with enhanced data
  Future<Map<String, dynamic>> requestRide({
    required String riderId,
    required Map<String, dynamic> pickupLocation,
    required Map<String, dynamic> destination,
    String? vehicleType,
    Map<String, dynamic>? preferences,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/rides/request'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'riderId': riderId,
          'pickupLocation': pickupLocation,
          'destination': destination,
          'vehicleType': vehicleType ?? 'standard',
          'preferences': preferences ?? {},
          'requestTime': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);

        // Also emit via socket for real-time updates
        SocketService.instance.socket?.emit('ride:request', {
          'rideId': data['rideId'],
          'riderId': riderId,
          'timestamp': DateTime.now().toIso8601String(),
        });

        return {
          'success': true,
          'rideId': data['rideId'],
          'estimatedWaitTime': data['estimatedWaitTime'] ?? 5,
          'estimatedFare': data['estimatedFare'] ?? 25,
          'distance': data['distance'] ?? 0,
          'message': data['message'] ?? 'Ride request created successfully',
        };
      } else {
        throw Exception('Failed to request ride: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error requesting ride: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Get real-time route with traffic data
  Future<Map<String, dynamic>> getEnhancedRoute({
    required List<Map<String, double>> waypoints,
    bool includeTraffic = true,
    bool includeAlternatives = true,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/routes/enhanced'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'waypoints': waypoints,
          'includeTraffic': includeTraffic,
          'includeAlternatives': includeAlternatives,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to get route: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting enhanced route: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Update driver location (for driver app)
  void updateDriverLocation({
    required String driverId,
    required String rideId,
    required double lat,
    required double lng,
    double? speed,
    double? bearing,
    double? accuracy,
  }) {
    if (_isConnected) {
      SocketService.instance.socket?.emit('driver:location_update', {
        'driverId': driverId,
        'rideId': rideId,
        'lat': lat,
        'lng': lng,
        'speed': speed ?? 0.0,
        'bearing': bearing ?? 0.0,
        'accuracy': accuracy ?? 0.0,
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Request route optimization
  void requestRouteOptimization({
    required String rideId,
    required Map<String, double> currentLocation,
    List<Map<String, double>>? additionalStops,
  }) {
    if (_isConnected) {
      SocketService.instance.socket?.emit('ride:optimize_route', {
        'rideId': rideId,
        'currentLocation': currentLocation,
        'additionalStops': additionalStops ?? [],
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Report traffic incident
  void reportTrafficIncident({
    required Map<String, double> location,
    required String incidentType,
    String? description,
    int? severity,
  }) {
    if (_isConnected) {
      SocketService.instance.socket?.emit('traffic:report_incident', {
        'location': location,
        'incidentType': incidentType,
        'description': description ?? '',
        'severity': severity ?? 1,
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Get authentication token (implement based on your auth system)
  Future<String> _getAuthToken() async {
    // Implement your authentication token retrieval logic here
    // This is a placeholder
    return 'your_auth_token_here';
  }

  /// Get route for a ride
  Future<Map<String, dynamic>> getRideRoute(String rideId) async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/rides/$rideId/route'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'route': data['route'],
        };
      } else {
        throw Exception('Failed to get route: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting ride route: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Accept a ride (for drivers)
  Future<Map<String, dynamic>> acceptRide(
      String rideId, String driverId) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/rides/$rideId/accept'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'driverId': driverId,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Also emit via socket for real-time updates
        SocketService.instance.socket?.emit('ride:accept', {
          'rideId': rideId,
          'driverId': driverId,
          'timestamp': DateTime.now().toIso8601String(),
        });

        return {
          'success': true,
          'message': data['message'],
          'ride': data['ride'],
        };
      } else {
        throw Exception('Failed to accept ride: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error accepting ride: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Start a ride (for drivers)
  Future<Map<String, dynamic>> startRide(String rideId, String driverId) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/rides/$rideId/start'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'driverId': driverId,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Also emit via socket for real-time updates
        SocketService.instance.socket?.emit('ride:start', {
          'rideId': rideId,
          'driverId': driverId,
          'timestamp': DateTime.now().toIso8601String(),
        });

        return {
          'success': true,
          'message': data['message'],
          'ride': data['ride'],
        };
      } else {
        throw Exception('Failed to start ride: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error starting ride: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Dispose resources
  void dispose() {
    _connectionMonitor?.cancel();
    _rideUpdatesController.close();
    _driverLocationController.close();
    _trafficUpdatesController.close();
    _connectionStatusController.close();
    debugPrint('Enhanced Backend Communication disposed');
  }
}
