# AI Features API Documentation

This document describes the advanced AI features API endpoints for the Smart Ride Sharing application.

## Base URL
```
http://localhost:3000/api/ai
```

## AI-Powered Ride Sharing

### 1. Analyze Optimal Route
Analyze the optimal route for adding a new passenger to an existing ride.

**Endpoint:** `POST /ride-sharing/analyze`

**Request Body:**
```json
{
  "driverId": "driver_123",
  "newPassenger": {
    "id": "passenger_456",
    "pickupLocation": {
      "lat": 24.7136,
      "lng": 46.6753,
      "address": "King Fahd Road, Riyadh"
    },
    "destination": {
      "lat": 24.7453,
      "lng": 46.6727,
      "address": "Olaya District, Riyadh"
    },
    "preferences": {
      "genderPreference": "same",
      "noSmoking": true
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "analysis": {
    "canAccept": true,
    "recommendation": "accept",
    "message": "Good match - acceptable with minor detour",
    "routeOptimization": {
      "originalDistance": 15000,
      "newDistance": 18000,
      "detourDistance": 3000,
      "originalDuration": 1200,
      "newDuration": 1440,
      "detourDuration": 240,
      "efficiencyScore": 0.75,
      "compatibilityScore": 0.9
    },
    "fareOptimization": {
      "originalFare": 45,
      "newFare": 32,
      "savings": 13
    },
    "environmentalImpact": {
      "co2Reduction": 2.4,
      "fuelSaved": 1.8
    }
  }
}
```

### 2. Get Passenger Suggestions
Get suggestions for additional passengers for an active ride.

**Endpoint:** `GET /ride-sharing/suggestions/:rideId`

**Query Parameters:**
- `radius` (optional): Search radius in kilometers (default: 5)
- `limit` (optional): Maximum number of suggestions (default: 5)

**Response:**
```json
{
  "success": true,
  "suggestions": [
    {
      "passenger": {
        "id": "passenger_789",
        "pickupLocation": {...},
        "destination": {...}
      },
      "analysis": {...},
      "priority": 0.85,
      "estimatedPickupTime": 8,
      "fareReduction": 15
    }
  ]
}
```

### 3. Compare Available Rides
Compare available rides for a passenger request.

**Endpoint:** `POST /ride-sharing/compare-rides`

**Request Body:**
```json
{
  "passengerRequest": {
    "id": "passenger_123",
    "pickupLocation": {...},
    "destination": {...},
    "preferences": {...}
  }
}
```

## Recommendation System

### 1. Get Rider Recommendations
Get recommended riders for a driver.

**Endpoint:** `GET /recommendations/riders/:driverId`

**Query Parameters:**
- `limit` (optional): Maximum number of recommendations (default: 10)

**Response:**
```json
{
  "success": true,
  "recommendations": [
    {
      "rider": {
        "id": "rider_123",
        "rating": 4.8,
        "pickupLocation": {...},
        "destination": {...}
      },
      "score": 0.92,
      "reasons": ["High rider rating", "Very close pickup location"],
      "compatibility": 0.85,
      "estimatedEarnings": 65,
      "riskLevel": 0.1
    }
  ]
}
```

### 2. Get Driver Recommendations
Get recommended drivers for a rider.

**Endpoint:** `GET /recommendations/drivers/:riderId`

**Query Parameters:**
- `limit` (optional): Maximum number of recommendations (default: 10)

**Response:**
```json
{
  "success": true,
  "recommendations": [
    {
      "driver": {
        "id": "driver_456",
        "name": "Ahmed Ali",
        "rating": 4.9,
        "vehicle": {...},
        "location": {...}
      },
      "score": 0.95,
      "reasons": ["Excellent driver rating", "Quick pickup time"],
      "compatibility": 0.88,
      "estimatedArrival": 5,
      "safetyScore": 0.96
    }
  ]
}
```

### 3. Learn from Interaction
Submit interaction data for machine learning.

**Endpoint:** `POST /recommendations/learn`

**Request Body:**
```json
{
  "userId": "user_123",
  "userType": "rider",
  "interaction": {
    "partnerId": "driver_456",
    "rating": 5,
    "tripLength": 12.5,
    "distance": 3.2,
    "partnerRating": 4.8,
    "feedback": "Excellent service"
  }
}
```

## Safety Features

### 1. Verify Identity
Verify user identity using face recognition.

**Endpoint:** `POST /safety/verify-identity`

**Request Body:**
```json
{
  "userId": "user_123",
  "userType": "driver",
  "imageData": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

**Response:**
```json
{
  "success": true,
  "verification": {
    "success": true,
    "verified": true,
    "confidence": 0.95,
    "message": "Identity verified successfully"
  }
}
```

### 2. Set Gender Preference
Set gender preference for safety.

**Endpoint:** `POST /safety/gender-preference`

**Request Body:**
```json
{
  "userId": "user_123",
  "preference": "same"
}
```

**Preference Options:**
- `"same"`: Same gender only
- `"opposite"`: Opposite gender only  
- `"any"`: No preference

### 3. Add Emergency Contact
Add emergency contact for a user.

**Endpoint:** `POST /safety/emergency-contact`

**Request Body:**
```json
{
  "userId": "user_123",
  "contact": {
    "name": "Sarah Ahmed",
    "phone": "+966501234567",
    "relationship": "sister",
    "isPrimary": true
  }
}
```

### 4. Start Safety Monitoring
Start safety monitoring for a ride.

**Endpoint:** `POST /safety/monitor/:rideId`

**Response:**
```json
{
  "success": true,
  "message": "Safety monitoring started"
}
```

## Voice Assistant

### 1. Process Voice Command
Process a voice command and return appropriate response.

**Endpoint:** `POST /voice/command`

**Request Body:**
```json
{
  "command": "طلب رحلة من الرياض إلى جدة",
  "userId": "user_123",
  "userType": "rider"
}
```

**Response:**
```json
{
  "success": true,
  "response": "جاري البحث عن سائق من الرياض إلى جدة...",
  "action": "request_ride",
  "data": {
    "pickup": "الرياض",
    "destination": "جدة",
    "needsConfirmation": true
  }
}
```

### 2. Get Supported Commands
Get list of supported voice commands.

**Endpoint:** `GET /voice/commands`

**Query Parameters:**
- `userType` (optional): "driver" or "rider"

**Response:**
```json
{
  "success": true,
  "commands": [
    "طلب رحلة",
    "إلغاء الرحلة", 
    "أين السائق",
    "مشاركة الرحلة",
    "مساعدة"
  ],
  "userType": "rider"
}
```

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information"
}
```

Common HTTP status codes:
- `400`: Bad Request - Invalid input data
- `404`: Not Found - Resource not found
- `500`: Internal Server Error - Server-side error

## Real-time Events

The application uses Socket.IO for real-time communication. Key events include:

### Ride Sharing Events
- `ride:passenger_suggestions` - New passenger suggestions for driver
- `ride:sharing_opportunity` - Ride sharing opportunity for passenger

### Safety Events  
- `safety:alert` - Safety alert notification
- `safety:emergency` - Emergency situation detected

### Voice Assistant Events
- `voice:command_processed` - Voice command processing result
- `voice:response` - Voice assistant response

## Authentication

All API endpoints require proper authentication. Include the user's authentication token in the request headers:

```
Authorization: Bearer <token>
```
