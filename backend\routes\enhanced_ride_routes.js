const express = require('express');
const router = express.Router();
const axios = require('axios');
const polyline = require('@mapbox/polyline');

// Enhanced ride request with real-time features
router.post('/request', async (req, res) => {
  try {
    const { riderId, pickupLocation, destination, vehicleType, preferences } = req.body;
    
    // Create enhanced ride object
    const ride = {
      id: `ride_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      riderId,
      pickupLocation,
      destination,
      vehicleType: vehicleType || 'standard',
      preferences: preferences || {},
      status: 'pending',
      requestTime: new Date().toISOString(),
      estimatedWaitTime: await calculateEstimatedWaitTime(pickupLocation),
      estimatedFare: await calculateEstimatedFare(pickupLocation, destination, vehicleType),
      route: null,
      driverId: null,
    };

    // Get optimized route with traffic data
    const routeData = await getEnhancedRoute([pickupLocation, destination], true);
    if (routeData.success) {
      ride.route = routeData.route;
      ride.estimatedDuration = routeData.duration;
      ride.estimatedDistance = routeData.distance;
    }

    // Store ride in database (implement your database logic)
    await storeRide(ride);

    // Emit to available drivers
    req.io.emit('ride:new_request', {
      rideId: ride.id,
      pickupLocation: ride.pickupLocation,
      destination: ride.destination,
      estimatedFare: ride.estimatedFare,
      vehicleType: ride.vehicleType,
      timestamp: ride.requestTime,
    });

    // Start driver matching process
    setTimeout(() => {
      findAndAssignDriver(ride, req.io);
    }, 1000);

    res.status(201).json({
      success: true,
      rideId: ride.id,
      estimatedWaitTime: ride.estimatedWaitTime,
      estimatedFare: ride.estimatedFare,
      estimatedDuration: ride.estimatedDuration,
      route: ride.route,
    });

  } catch (error) {
    console.error('Error creating ride request:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create ride request',
      details: error.message,
    });
  }
});

// Enhanced route calculation with traffic data
router.post('/enhanced-route', async (req, res) => {
  try {
    const { waypoints, includeTraffic, includeAlternatives } = req.body;
    
    const routeData = await getEnhancedRoute(waypoints, includeTraffic, includeAlternatives);
    
    res.json(routeData);
  } catch (error) {
    console.error('Error calculating enhanced route:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to calculate route',
      details: error.message,
    });
  }
});

// Real-time driver location update
router.post('/driver-location', async (req, res) => {
  try {
    const { driverId, rideId, lat, lng, speed, bearing, accuracy } = req.body;
    
    // Update driver location in database
    await updateDriverLocation(driverId, { lat, lng, speed, bearing, accuracy });
    
    // Emit real-time update to rider
    req.io.to(`ride_${rideId}`).emit('driver:location_update', {
      driverId,
      rideId,
      lat,
      lng,
      speed: speed || 0,
      bearing: bearing || 0,
      accuracy: accuracy || 0,
      timestamp: new Date().toISOString(),
    });

    // Check for arrival at waypoints
    const ride = await getRideById(rideId);
    if (ride) {
      await checkWaypointArrival(ride, { lat, lng }, req.io);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating driver location:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update driver location',
    });
  }
});

// Traffic incident reporting
router.post('/traffic-incident', async (req, res) => {
  try {
    const { location, incidentType, description, severity } = req.body;
    
    const incident = {
      id: `incident_${Date.now()}`,
      location,
      incidentType,
      description: description || '',
      severity: severity || 1,
      timestamp: new Date().toISOString(),
      status: 'active',
    };

    // Store incident in database
    await storeTrafficIncident(incident);

    // Notify affected rides
    await notifyAffectedRides(incident, req.io);

    res.json({
      success: true,
      incidentId: incident.id,
    });
  } catch (error) {
    console.error('Error reporting traffic incident:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to report traffic incident',
    });
  }
});

// Helper Functions

async function getEnhancedRoute(waypoints, includeTraffic = true, includeAlternatives = false) {
  try {
    // Format waypoints for OSRM API
    const waypointsStr = waypoints
      .map(point => `${point.lng},${point.lat}`)
      .join(';');
    
    // Build OSRM URL with traffic considerations
    let url = `https://router.project-osrm.org/route/v1/driving/${waypointsStr}?overview=full&geometries=polyline&steps=true`;
    
    if (includeAlternatives) {
      url += '&alternatives=true&alternatives=3';
    }

    const response = await axios.get(url);
    
    if (response.data.code !== 'Ok' || response.data.routes.length === 0) {
      throw new Error('Failed to calculate route');
    }

    const mainRoute = response.data.routes[0];
    const decodedPoints = polyline.decode(mainRoute.geometry);
    
    // Convert to LatLng format
    const routePoints = decodedPoints.map(point => ({
      lat: point[0],
      lng: point[1],
    }));

    // Get traffic data if requested
    let trafficData = null;
    if (includeTraffic) {
      trafficData = await getTrafficData(routePoints);
    }

    // Process alternative routes
    let alternatives = [];
    if (includeAlternatives && response.data.routes.length > 1) {
      alternatives = response.data.routes.slice(1).map(route => ({
        geometry: route.geometry,
        distance: route.distance,
        duration: route.duration,
        points: polyline.decode(route.geometry).map(point => ({
          lat: point[0],
          lng: point[1],
        })),
      }));
    }

    return {
      success: true,
      route: {
        encodedPolyline: mainRoute.geometry,
        points: routePoints,
        distance: mainRoute.distance,
        duration: mainRoute.duration,
        steps: mainRoute.legs[0]?.steps || [],
      },
      traffic: trafficData,
      alternatives,
      timestamp: new Date().toISOString(),
    };

  } catch (error) {
    console.error('Error getting enhanced route:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

async function getTrafficData(routePoints) {
  try {
    // Simulate traffic data (in real implementation, use traffic API)
    const trafficLevels = ['light', 'normal', 'moderate', 'heavy'];
    const randomLevel = trafficLevels[Math.floor(Math.random() * trafficLevels.length)];
    
    return {
      level: randomLevel,
      delay: randomLevel === 'heavy' ? 15 : randomLevel === 'moderate' ? 8 : 0,
      incidents: [],
      lastUpdated: new Date().toISOString(),
    };
  } catch (error) {
    console.error('Error getting traffic data:', error);
    return null;
  }
}

async function calculateEstimatedWaitTime(pickupLocation) {
  // Simulate driver availability calculation
  const baseWaitTime = 5; // 5 minutes base
  const randomFactor = Math.random() * 10; // 0-10 minutes additional
  return Math.round(baseWaitTime + randomFactor);
}

async function calculateEstimatedFare(pickupLocation, destination, vehicleType) {
  // Simple fare calculation (implement your pricing logic)
  const baseFare = vehicleType === 'premium' ? 15 : 10;
  const distance = calculateDistance(pickupLocation, destination);
  const distanceFare = distance * (vehicleType === 'premium' ? 2.5 : 1.8);
  return Math.round((baseFare + distanceFare) * 100) / 100;
}

function calculateDistance(point1, point2) {
  const R = 6371; // Earth's radius in km
  const dLat = (point2.lat - point1.lat) * Math.PI / 180;
  const dLon = (point2.lng - point1.lng) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

async function findAndAssignDriver(ride, io) {
  try {
    // Simulate driver assignment (implement your driver matching logic)
    const mockDriverId = `driver_${Math.random().toString(36).substr(2, 9)}`;
    
    // Update ride with assigned driver
    ride.driverId = mockDriverId;
    ride.status = 'accepted';
    ride.acceptedAt = new Date().toISOString();

    // Emit to rider
    io.to(`ride_${ride.id}`).emit('ride:driver_assigned', {
      rideId: ride.id,
      driverId: mockDriverId,
      driverInfo: {
        name: 'John Driver',
        rating: 4.8,
        vehicleInfo: 'Toyota Camry - ABC123',
        estimatedArrival: 8,
      },
      timestamp: ride.acceptedAt,
    });

    // Start simulated driver movement
    setTimeout(() => {
      simulateDriverMovement(ride, io);
    }, 2000);

  } catch (error) {
    console.error('Error assigning driver:', error);
  }
}

async function simulateDriverMovement(ride, io) {
  // Simulate driver moving towards pickup
  const driverLocation = {
    lat: ride.pickupLocation.lat + (Math.random() - 0.5) * 0.01,
    lng: ride.pickupLocation.lng + (Math.random() - 0.5) * 0.01,
  };

  const interval = setInterval(() => {
    // Move driver closer to pickup
    driverLocation.lat += (ride.pickupLocation.lat - driverLocation.lat) * 0.1;
    driverLocation.lng += (ride.pickupLocation.lng - driverLocation.lng) * 0.1;

    const speed = 30 + Math.random() * 20; // 30-50 km/h
    const bearing = Math.random() * 360;

    // Emit location update
    io.to(`ride_${ride.id}`).emit('driver:location_update', {
      rideId: ride.id,
      driverId: ride.driverId,
      lat: driverLocation.lat,
      lng: driverLocation.lng,
      speed,
      bearing,
      timestamp: new Date().toISOString(),
    });

    // Check if arrived at pickup
    const distance = calculateDistance(driverLocation, ride.pickupLocation);
    if (distance < 0.1) { // Within 100 meters
      clearInterval(interval);
      
      // Emit pickup arrival
      io.to(`ride_${ride.id}`).emit('ride:driver_arrived_pickup', {
        rideId: ride.id,
        driverId: ride.driverId,
        timestamp: new Date().toISOString(),
      });
    }
  }, 3000); // Update every 3 seconds
}

// Database integration functions
const dbService = require('../services/database_service');
const logger = require('../utils/logger');

async function storeRide(ride) {
  try {
    const storedRide = await dbService.createRide({
      riderId: ride.riderId,
      pickupLocation: ride.pickupLocation,
      destination: ride.destination,
      vehicleType: ride.vehicleType,
      preferences: ride.preferences,
      estimatedFare: ride.estimatedFare,
      estimatedDuration: ride.estimatedDuration,
      estimatedDistance: ride.estimatedDistance,
      route: ride.route
    });

    logger.info(`Ride stored successfully: ${storedRide.id}`);
    return storedRide;
  } catch (error) {
    logger.error(`Error storing ride: ${error.message}`);
    throw error;
  }
}

async function updateDriverLocation(driverId, location) {
  try {
    const updatedDriver = await dbService.updateDriver(driverId, {
      location: {
        lat: location.lat,
        lng: location.lng,
        speed: location.speed || 0,
        bearing: location.bearing || 0,
        accuracy: location.accuracy || 0,
        lastUpdated: new Date()
      }
    });

    if (updatedDriver) {
      logger.info(`Driver location updated: ${driverId}`);
    }

    return updatedDriver;
  } catch (error) {
    logger.error(`Error updating driver location: ${error.message}`);
    throw error;
  }
}

async function getRideById(rideId) {
  try {
    const ride = await dbService.getRideById(rideId);
    return ride;
  } catch (error) {
    logger.error(`Error getting ride by ID: ${error.message}`);
    return null;
  }
}

async function checkWaypointArrival(ride, currentLocation, io) {
  try {
    if (!ride || !ride.route || !ride.route.waypoints) {
      return;
    }

    const arrivalThreshold = 100; // 100 meters

    for (const waypoint of ride.route.waypoints) {
      const distance = calculateDistance(currentLocation, waypoint);

      if (distance <= arrivalThreshold) {
        // Driver has arrived at this waypoint
        logger.info(`Driver arrived at waypoint for ride ${ride.id}`);

        // Emit arrival event
        io.to(`ride_${ride.id}`).emit('driver:waypoint_arrival', {
          rideId: ride.id,
          waypoint: waypoint,
          timestamp: new Date().toISOString()
        });

        // Update ride status if this is the final destination
        if (waypoint === ride.route.waypoints[ride.route.waypoints.length - 1]) {
          await dbService.updateRide(ride.id, { status: 'completed' });

          io.to(`ride_${ride.id}`).emit('ride:completed', {
            rideId: ride.id,
            timestamp: new Date().toISOString()
          });
        }

        break;
      }
    }
  } catch (error) {
    logger.error(`Error checking waypoint arrival: ${error.message}`);
  }
}

async function storeTrafficIncident(incident) {
  try {
    // For now, store in memory (implement proper database storage later)
    const incidents = global.trafficIncidents || [];
    incidents.push(incident);
    global.trafficIncidents = incidents;

    logger.info(`Traffic incident stored: ${incident.id}`);
    return incident;
  } catch (error) {
    logger.error(`Error storing traffic incident: ${error.message}`);
    throw error;
  }
}

async function notifyAffectedRides(incident, io) {
  try {
    // Get all active rides
    const activeRides = await dbService.getRides({ status: ['accepted', 'in_progress'] });

    const affectedRides = [];
    const incidentRadius = 5; // 5 km radius

    for (const ride of activeRides) {
      if (ride.route && ride.route.waypoints) {
        // Check if any waypoint is within the incident radius
        for (const waypoint of ride.route.waypoints) {
          const distance = calculateDistance(incident.location, waypoint);

          if (distance <= incidentRadius) {
            affectedRides.push(ride);

            // Notify the ride participants
            io.to(`ride_${ride.id}`).emit('traffic:incident_alert', {
              rideId: ride.id,
              incident: incident,
              estimatedDelay: Math.round(incident.severity * 5), // 5 minutes per severity level
              timestamp: new Date().toISOString()
            });

            break;
          }
        }
      }
    }

    logger.info(`Notified ${affectedRides.length} rides about traffic incident ${incident.id}`);
    return affectedRides;
  } catch (error) {
    logger.error(`Error notifying affected rides: ${error.message}`);
    return [];
  }
}

module.exports = router;
