import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math' as math;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:project/core/config/api_config.dart';
import 'package:project/core/services/socket_service.dart';
import 'package:project/core/services/enhanced_ride_animation_service.dart';
import 'package:project/core/services/enhanced_backend_communication.dart';
import 'dart:async';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';

/// Enhanced Home Screen with Backend Integration and AI Features
/// This screen provides real ride booking functionality with AI assistance
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TextEditingController _searchController;
  late TextEditingController _destinationController;
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  // State variables
  bool _isRequestingRide = false;
  bool _isListeningToVoice = false;
  String? _currentRideId;
  String? _rideStatus;
  Map<String, dynamic>? _currentLocation;
  Map<String, dynamic>? _destinationLocation;
  List<Map<String, dynamic>> _recentDestinations = [];
  String _aiSuggestion = '';
  bool _showAiSuggestion = false;

  // Google Maps variables
  final Completer<GoogleMapController> _mapController = Completer();
  LatLng? _currentPosition;
  final LatLng _defaultPosition =
      const LatLng(37.7749, -122.4194); // San Francisco
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  bool _isMapInitialized = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _destinationController = TextEditingController();

    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    _initializeApp();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _destinationController.dispose();
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _initializeApp() async {
    // Initialize enhanced backend communication
    EnhancedBackendCommunication.instance.initialize();

    // Initialize socket connection
    SocketService.instance.connect();

    // Load user data and recent destinations
    await _loadUserData();
    await _loadRecentDestinations();

    // Initialize map and location
    await _getCurrentLocation();

    // Set up socket listeners
    _setupSocketListeners();

    // Set up enhanced communication listeners
    _setupEnhancedCommunicationListeners();

    // Get AI suggestions
    await _getAiSuggestions();
  }

  /// Setup enhanced communication listeners
  void _setupEnhancedCommunicationListeners() {
    // Listen to ride updates
    EnhancedBackendCommunication.instance.rideUpdates.listen((update) {
      _handleRideUpdate(update);
    });

    // Listen to driver location updates
    EnhancedBackendCommunication.instance.driverLocationUpdates
        .listen((location) {
      _handleDriverLocationUpdate(location);
    });

    // Listen to traffic updates
    EnhancedBackendCommunication.instance.trafficUpdates.listen((traffic) {
      _handleTrafficUpdate(traffic);
    });

    // Listen to connection status
    EnhancedBackendCommunication.instance.connectionStatus.listen((status) {
      _handleConnectionStatusChange(status);
    });
  }

  /// Handle ride updates from enhanced communication
  void _handleRideUpdate(Map<String, dynamic> update) {
    final type = update['type'] as String;
    final data = update['data'] as Map<String, dynamic>;

    switch (type) {
      case 'status_update':
        setState(() {
          _rideStatus = data['status'] ?? _rideStatus;
          _currentRideId = data['rideId'] ?? _currentRideId;
        });
        _showRideStatusDialog('Ride Update', 'Status: ${data['status']}');
        break;
      case 'driver_assigned':
        setState(() {
          _currentRideId = data['rideId'] ?? _currentRideId;
        });
        _showRideStatusDialog('Driver Assigned',
            'Driver ${data['driverInfo']['name']} is coming!');
        _initializeRideAnimation(data);
        break;
      case 'eta_update':
        _showSnackBar('ETA Updated: ${data['estimatedArrival']} minutes');
        break;
    }
  }

  /// Handle driver location updates
  void _handleDriverLocationUpdate(Map<String, dynamic> location) {
    if (location['rideId'] == _currentRideId) {
      // The enhanced animation service will handle the visual updates
      debugPrint(
          'Driver location updated: ${location['lat']}, ${location['lng']}');
    }
  }

  /// Handle traffic updates
  void _handleTrafficUpdate(Map<String, dynamic> traffic) {
    final level = traffic['level'] as String;
    final delay = traffic['delay'] as double;

    if (delay > 0) {
      _showSnackBar(
          'Traffic Alert: ${level.toUpperCase()} traffic, ${delay.toInt()} min delay');
    }
  }

  /// Handle connection status changes
  void _handleConnectionStatusChange(String status) {
    switch (status) {
      case 'connected':
        _showSnackBar('Connected to server', backgroundColor: Colors.green);
        break;
      case 'disconnected':
        _showSnackBar('Connection lost, trying to reconnect...',
            backgroundColor: Colors.orange);
        break;
      case 'failed':
        _showSnackBar('Connection failed', backgroundColor: Colors.red);
        break;
    }
  }

  /// Initialize ride animation when driver is assigned
  void _initializeRideAnimation(Map<String, dynamic> driverData) {
    if (_currentLocation != null && _destinationLocation != null) {
      // Initialize enhanced ride animation service
      EnhancedRideAnimationService.instance.startRideAnimation(
        rideId: _currentRideId!,
        driverId: driverData['driverId'],
        riderId: 'current_user', // Get from user data
        driverLocation: LatLng(
          _currentLocation!['lat'] +
              (0.01 *
                  (1 -
                      2 *
                          (DateTime.now().millisecond %
                              2))), // Mock driver location
          _currentLocation!['lng'] +
              (0.01 * (1 - 2 * (DateTime.now().second % 2))),
        ),
        pickupLocation:
            LatLng(_currentLocation!['lat'], _currentLocation!['lng']),
        destinationLocation:
            LatLng(_destinationLocation!['lat'], _destinationLocation!['lng']),
      );
    }
  }

  /// Show snackbar with custom styling
  void _showSnackBar(String message, {Color? backgroundColor}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        // Use default location if location services are disabled
        setState(() {
          _currentPosition = _defaultPosition;
          _markers.add(
            Marker(
              markerId: const MarkerId('default_location'),
              position: _defaultPosition,
              infoWindow: const InfoWindow(title: 'Default Location'),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueBlue),
            ),
          );
        });
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // Use default location if permission denied
          setState(() {
            _currentPosition = _defaultPosition;
            _markers.add(
              Marker(
                markerId: const MarkerId('default_location'),
                position: _defaultPosition,
                infoWindow: const InfoWindow(title: 'Default Location'),
                icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueBlue),
              ),
            );
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // Use default location if permission permanently denied
        setState(() {
          _currentPosition = _defaultPosition;
          _markers.add(
            Marker(
              markerId: const MarkerId('default_location'),
              position: _defaultPosition,
              infoWindow: const InfoWindow(title: 'Default Location'),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueBlue),
            ),
          );
        });
        return;
      }

      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      if (mounted) {
        setState(() {
          _currentPosition = LatLng(position.latitude, position.longitude);
          _markers.clear();
          _markers.add(
            Marker(
              markerId: const MarkerId('current_location'),
              position: _currentPosition!,
              infoWindow: const InfoWindow(
                title: 'Your Location',
                snippet: 'You are here',
              ),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueBlue),
            ),
          );
        });

        // Update current location data
        _currentLocation = {
          'lat': position.latitude,
          'lng': position.longitude,
          'address': 'Current Location'
        };

        // Move camera to current location if map is initialized
        if (_isMapInitialized && _mapController.isCompleted) {
          final controller = await _mapController.future;
          controller.animateCamera(
            CameraUpdate.newLatLngZoom(_currentPosition!, 16.0),
          );
        }
      }
    } catch (e) {
      debugPrint('Error getting current location: $e');
      // Use default location on error
      if (mounted) {
        setState(() {
          _currentPosition = _defaultPosition;
          _markers.add(
            Marker(
              markerId: const MarkerId('default_location'),
              position: _defaultPosition,
              infoWindow: const InfoWindow(title: 'Default Location'),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueBlue),
            ),
          );
        });
      }
    }
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id') ??
          'user_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString('user_id', userId);

      // Set default current location (San Francisco)
      _currentLocation = {
        'lat': 37.7749,
        'lng': -122.4194,
        'address': 'San Francisco, CA'
      };
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  Future<void> _loadRecentDestinations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recentJson = prefs.getString('recent_destinations');
      if (recentJson != null) {
        final List<dynamic> decoded = jsonDecode(recentJson);
        setState(() {
          _recentDestinations = decoded.cast<Map<String, dynamic>>();
        });
      }
    } catch (e) {
      debugPrint('Error loading recent destinations: $e');
    }
  }

  void _setupSocketListeners() {
    // Listen for ride status updates
    SocketService.instance.listenTo('ride:accepted', (data) async {
      if (mounted) {
        setState(() {
          _rideStatus = 'accepted';
          _currentRideId = data['rideId'];
        });

        // Show driver info and get route
        _showDriverAcceptedDialog(data);

        // Fetch and display the route
        await _fetchAndDisplayRoute(data['rideId']);
      }
    });

    SocketService.instance.listenTo('ride:started', (data) {
      if (mounted) {
        setState(() {
          _rideStatus = 'in_progress';
        });
        _showRideStatusDialog('Ride Started!', 'Your driver is on the way.');
      }
    });

    SocketService.instance.listenTo('ride:completed', (data) {
      if (mounted) {
        setState(() {
          _rideStatus = 'completed';
          _currentRideId = null;
        });
        _showRideStatusDialog(
            'Ride Completed!', 'Thank you for using our service.');
      }
    });
  }

  Future<void> _getAiSuggestions() async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/voice/command'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'command': 'suggest popular destinations',
          'auto_speak': false,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _aiSuggestion = data['response'] ?? 'No suggestions available';
          _showAiSuggestion = true;
        });
        _slideController.forward();
      }
    } catch (e) {
      debugPrint('Error getting AI suggestions: $e');
    }
  }

  void _showRideStatusDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.info, color: Colors.blue),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showDriverAcceptedDialog(Map<String, dynamic> data) {
    final driverInfo = data['driverInfo'] ?? {};

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('Driver Found!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Driver: ${driverInfo['name'] ?? 'Unknown Driver'}'),
            Text('Rating: ${driverInfo['rating'] ?? 'N/A'} ⭐'),
            Text('Vehicle: ${driverInfo['car'] ?? 'Unknown'}'),
            Text('Plate: ${driverInfo['plate'] ?? 'N/A'}'),
            Text('ETA: ${driverInfo['eta'] ?? 'Calculating...'}'),
            const SizedBox(height: 16),
            const Text('Your driver is on the way!',
                style: TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Add call driver functionality
              _showSnackBar('Calling driver...');
            },
            child: const Text('Call Driver'),
          ),
        ],
      ),
    );
  }

  Future<void> _fetchAndDisplayRoute(String rideId) async {
    try {
      final routeResult =
          await EnhancedBackendCommunication.instance.getRideRoute(rideId);

      if (routeResult['success'] == true && routeResult['route'] != null) {
        final route = routeResult['route'];
        final coordinates = route['coordinates'] as List<dynamic>?;

        if (coordinates != null && coordinates.isNotEmpty) {
          // Convert coordinates to LatLng points
          final List<LatLng> routePoints = coordinates.map((coord) {
            return LatLng(
                coord[1], coord[0]); // Note: coordinates are [lng, lat]
          }).toList();

          // Create polyline for the route
          setState(() {
            _polylines.clear();
            _polylines.add(
              Polyline(
                polylineId: const PolylineId('ride_route'),
                points: routePoints,
                color: Colors.blue,
                width: 5,
                patterns: [PatternItem.dash(20), PatternItem.gap(10)],
              ),
            );
          });

          // Animate camera to show the full route
          if (_mapController.isCompleted) {
            final controller = await _mapController.future;

            // Calculate bounds for the route
            double minLat = routePoints.first.latitude;
            double maxLat = routePoints.first.latitude;
            double minLng = routePoints.first.longitude;
            double maxLng = routePoints.first.longitude;

            for (final point in routePoints) {
              minLat = math.min(minLat, point.latitude);
              maxLat = math.max(maxLat, point.latitude);
              minLng = math.min(minLng, point.longitude);
              maxLng = math.max(maxLng, point.longitude);
            }

            final bounds = LatLngBounds(
              southwest: LatLng(minLat, minLng),
              northeast: LatLng(maxLat, maxLng),
            );

            controller.animateCamera(
              CameraUpdate.newLatLngBounds(bounds, 100.0),
            );
          }

          _showSnackBar('Route loaded successfully!',
              backgroundColor: Colors.green);
        }
      } else {
        _showSnackBar('Could not load route', backgroundColor: Colors.orange);
      }
    } catch (e) {
      debugPrint('Error fetching route: $e');
      _showSnackBar('Error loading route: $e', backgroundColor: Colors.red);
    }
  }

  Future<void> _requestRide() async {
    if (_searchController.text.isEmpty && _destinationLocation == null) {
      _showSnackBar('Please enter a destination or tap on the map');
      return;
    }

    setState(() {
      _isRequestingRide = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id') ??
          'user_${DateTime.now().millisecondsSinceEpoch}';

      // Create destination location from input or map selection
      if (_destinationLocation == null) {
        _destinationLocation = {
          'lat': 37.7849, // Mock destination coordinates
          'lng': -122.4094,
          'address': _searchController.text,
        };
      } else if (_searchController.text.isNotEmpty) {
        _destinationLocation!['address'] = _searchController.text;
      }

      // Use enhanced backend communication for ride request
      final result = await EnhancedBackendCommunication.instance.requestRide(
        riderId: userId,
        pickupLocation: _currentLocation!,
        destination: _destinationLocation!,
        vehicleType: 'standard',
        preferences: {
          'allowSharing': true,
          'preferredRoute': 'fastest',
        },
      );

      if (result['success'] == true) {
        setState(() {
          _currentRideId = result['rideId'];
          _rideStatus = 'pending';
        });

        // Save to recent destinations
        await _saveToRecentDestinations(_destinationLocation!);

        // Get AI prediction for ride sharing
        await _getAiRideSharingPrediction();

        // Show success message with enhanced details
        _showSnackBar(
          'Ride requested! ETA: ${result['estimatedWaitTime']} min, Fare: \$${result['estimatedFare']}',
          backgroundColor: Colors.green,
        );

        // Initialize enhanced animation service with map controller
        if (_mapController.isCompleted) {
          final controller = await _mapController.future;
          EnhancedRideAnimationService.instance.initialize(
            mapController: controller,
            onMarkersUpdate: (markers) {
              setState(() {
                _markers = markers;
              });
            },
            onPolylinesUpdate: (polylines) {
              setState(() {
                _polylines = polylines;
              });
            },
            onDriverLocationUpdate: (position, speed, bearing) {
              debugPrint(
                  'Driver at: ${position.latitude}, ${position.longitude}, Speed: $speed km/h');
            },
            onRideStatusUpdate: (status, data) {
              setState(() {
                _rideStatus = status;
              });
              _showRideStatusDialog('Ride Status', 'Status: $status');
            },
            onRouteProgressUpdate: (progress, distance, eta) {
              debugPrint(
                  'Route progress: ${(progress * 100).toStringAsFixed(1)}%');
            },
            onRideEventUpdate: (eventType, message) {
              _showSnackBar('$eventType: $message');
            },
          );
        }
      } else {
        throw Exception(result['error'] ?? 'Failed to request ride');
      }
    } catch (e) {
      debugPrint('Error requesting ride: $e');
      _showSnackBar('Error requesting ride: $e', backgroundColor: Colors.red);
    } finally {
      setState(() {
        _isRequestingRide = false;
      });
    }
  }

  Future<void> _getAiRideSharingPrediction() async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/ride-sharing/predict'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'original_distance': 10.0,
          'distance_after_adding_rider': 12.0,
          'new_rider_distance': 3.5,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _showAiPredictionDialog(data);
      }
    } catch (e) {
      debugPrint('Error getting AI prediction: $e');
    }
  }

  Future<void> _saveToRecentDestinations(
      Map<String, dynamic> destination) async {
    try {
      // Add to recent destinations (max 5)
      _recentDestinations.insert(0, destination);
      if (_recentDestinations.length > 5) {
        _recentDestinations = _recentDestinations.take(5).toList();
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          'recent_destinations', jsonEncode(_recentDestinations));

      setState(() {});
    } catch (e) {
      debugPrint('Error saving recent destination: $e');
    }
  }

  void _showAiPredictionDialog(Map<String, dynamic> prediction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.psychology, color: Colors.purple),
            SizedBox(width: 8),
            Text('AI Ride Sharing Analysis'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                'AI Recommendation: ${prediction['shouldAddRider'] ? 'Share this ride' : 'Keep ride private'}'),
            const SizedBox(height: 8),
            Text(
                'Confidence: ${(prediction['score'] * 100).toStringAsFixed(1)}%'),
            if (prediction['efficiency'] != null)
              Text('Efficiency: ${prediction['efficiency']}%'),
            const SizedBox(height: 16),
            const Text('Benefits of ride sharing:',
                style: TextStyle(fontWeight: FontWeight.bold)),
            const Text('• Reduce costs'),
            const Text('• Environmental friendly'),
            const Text('• Meet new people'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (prediction['shouldAddRider'])
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content:
                          Text('Looking for ride sharing opportunities...')),
                );
              },
              child: const Text('Find Sharing'),
            ),
        ],
      ),
    );
  }

  Future<void> _startVoiceCommand() async {
    setState(() {
      _isListeningToVoice = true;
    });
    _pulseController.repeat(reverse: true);

    try {
      // Simulate voice recognition (in real app, use speech_to_text package)
      await Future.delayed(const Duration(seconds: 3));

      final voiceCommand = 'Take me to the airport'; // Mock voice input

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/voice/command'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'command': voiceCommand,
          'auto_speak': true,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _searchController.text = 'Airport';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Voice command: ${data['response']}')),
        );
      }
    } catch (e) {
      debugPrint('Error processing voice command: $e');
    } finally {
      setState(() {
        _isListeningToVoice = false;
      });
      _pulseController.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Smart Ride Sharing'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Stack(
              children: [
                const Icon(Icons.notifications),
                if (_currentRideId != null)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 12,
                        minHeight: 12,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: () {
              if (_currentRideId != null) {
                _showRideStatusDialog('Current Ride',
                    'Ride ID: $_currentRideId\nStatus: $_rideStatus');
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('No active rides')),
                );
              }
            },
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            const DrawerHeader(
              decoration: BoxDecoration(
                color: Colors.blue,
              ),
              child: Text(
                'Menu',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.home),
              title: const Text('Home'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('Ride History'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Ride History - Coming Soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Profile'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Profile - Coming Soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Settings'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Settings - Coming Soon')),
                );
              },
            ),
          ],
        ),
      ),
      body: Stack(
        children: [
          // Full screen Google Map
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: _currentPosition ?? _defaultPosition,
              zoom: 16.0,
            ),
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            markers: _markers,
            polylines: _polylines,
            onMapCreated: (GoogleMapController controller) {
              _mapController.complete(controller);
              setState(() {
                _isMapInitialized = true;
              });
              // Move to current location if available
              if (_currentPosition != null) {
                controller.animateCamera(
                  CameraUpdate.newLatLngZoom(_currentPosition!, 16.0),
                );
              }
            },
            onTap: (LatLng position) {
              // Add destination marker on tap
              setState(() {
                _markers.removeWhere(
                    (marker) => marker.markerId.value == 'destination');
                _markers.add(
                  Marker(
                    markerId: const MarkerId('destination'),
                    position: position,
                    infoWindow: const InfoWindow(
                      title: 'Destination',
                      snippet: 'Tap to select',
                    ),
                    icon: BitmapDescriptor.defaultMarkerWithHue(
                        BitmapDescriptor.hueRed),
                  ),
                );
                _destinationLocation = {
                  'lat': position.latitude,
                  'lng': position.longitude,
                  'address': 'Selected Location'
                };
              });
            },
          ),

          // Top overlays
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [
                // Status bar for active ride
                if (_currentRideId != null) _buildRideStatusBar(),

                // AI Suggestion banner
                if (_showAiSuggestion) _buildAiSuggestionBanner(),

                // Enhanced search section
                _buildSearchSection(),
              ],
            ),
          ),

          // Bottom overlays
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Bottom buttons
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isRequestingRide ? null : _requestRide,
                            icon: _isRequestingRide
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white),
                                    ),
                                  )
                                : const Icon(Icons.local_taxi),
                            label: Text(_isRequestingRide
                                ? 'Requesting...'
                                : 'Request Ride'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[600],
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _isListeningToVoice
                                  ? _pulseAnimation.value
                                  : 1.0,
                              child: ElevatedButton.icon(
                                onPressed: _isListeningToVoice
                                    ? null
                                    : _startVoiceCommand,
                                icon: Icon(_isListeningToVoice
                                    ? Icons.mic
                                    : Icons.mic_none),
                                label: Text(_isListeningToVoice
                                    ? 'Listening...'
                                    : 'Voice'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _isListeningToVoice
                                      ? Colors.red
                                      : Colors.green[600],
                                  foregroundColor: Colors.white,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  // Quick actions
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildQuickAction(
                          icon: Icons.share,
                          label: 'Share Ride',
                          onTap: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text('Share Ride - Coming Soon')),
                            );
                          },
                        ),
                        _buildQuickAction(
                          icon: Icons.schedule,
                          label: 'Schedule',
                          onTap: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text('Schedule Ride - Coming Soon')),
                            );
                          },
                        ),
                        _buildQuickAction(
                          icon: Icons.favorite,
                          label: 'Favorites',
                          onTap: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content:
                                      Text('Favorite Places - Coming Soon')),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),

          // Floating action button for current location
          Positioned(
            bottom: 200,
            right: 16,
            child: FloatingActionButton(
              onPressed: () async {
                await _getCurrentLocation();
                if (_currentPosition != null && _mapController.isCompleted) {
                  final controller = await _mapController.future;
                  controller.animateCamera(
                    CameraUpdate.newLatLngZoom(_currentPosition!, 16.0),
                  );
                }
              },
              backgroundColor: Colors.white,
              child: const Icon(Icons.my_location, color: Colors.blue),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRideStatusBar() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _rideStatus == 'pending'
            ? Colors.orange[100]
            : _rideStatus == 'accepted'
                ? Colors.blue[100]
                : _rideStatus == 'in_progress'
                    ? Colors.green[100]
                    : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _rideStatus == 'pending'
              ? Colors.orange
              : _rideStatus == 'accepted'
                  ? Colors.blue
                  : _rideStatus == 'in_progress'
                      ? Colors.green
                      : Colors.grey,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _rideStatus == 'pending'
                ? Icons.hourglass_empty
                : _rideStatus == 'accepted'
                    ? Icons.check_circle
                    : _rideStatus == 'in_progress'
                        ? Icons.directions_car
                        : Icons.info,
            color: _rideStatus == 'pending'
                ? Colors.orange
                : _rideStatus == 'accepted'
                    ? Colors.blue
                    : _rideStatus == 'in_progress'
                        ? Colors.green
                        : Colors.grey,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Ride',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                Text(
                  'Status: ${_rideStatus?.toUpperCase()}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () => _showRideStatusDialog('Ride Details',
                'Ride ID: $_currentRideId\nStatus: $_rideStatus'),
            child: const Text('Details'),
          ),
        ],
      ),
    );
  }

  Widget _buildAiSuggestionBanner() {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.purple[100]!, Colors.blue[100]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            const Icon(Icons.psychology, color: Colors.purple),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'AI Suggestion',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  Text(
                    _aiSuggestion,
                    style: TextStyle(color: Colors.grey[700]),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () {
                setState(() {
                  _showAiSuggestion = false;
                });
                _slideController.reverse();
              },
              icon: const Icon(Icons.close, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Main search bar
          Card(
            elevation: 4,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: 'Where would you like to go?',
                  border: InputBorder.none,
                  icon: Icon(Icons.search, color: Colors.blue),
                  suffixIcon: Icon(Icons.my_location, color: Colors.grey),
                ),
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    _requestRide();
                  }
                },
              ),
            ),
          ),

          // Recent destinations
          if (_recentDestinations.isNotEmpty) ...[
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Recent Destinations',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _recentDestinations.length,
                itemBuilder: (context, index) {
                  final destination = _recentDestinations[index];
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: ActionChip(
                      label: Text(destination['address']),
                      onPressed: () {
                        _searchController.text = destination['address'];
                      },
                      backgroundColor: Colors.blue[50],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: Colors.blue,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
