/**
 * AI Features Routes
 * 
 * This file contains API routes for advanced AI features including:
 * - AI-powered ride sharing
 * - Recommendation system
 * - Safety features with face recognition
 */

const express = require('express');
const router = express.Router();
const aiRideSharingService = require('../services/ai_ride_sharing_service');
const recommendationService = require('../services/recommendation_service');
const safetyService = require('../services/safety_service');
const voiceAssistantService = require('../services/voice_assistant_service');
const dbService = require('../services/database_service');
const logger = require('../utils/logger');

// AI Ride Sharing Routes

/**
 * Analyze optimal route for adding a new passenger
 */
router.post('/ride-sharing/analyze', async (req, res) => {
  try {
    const { driverId, newPassenger } = req.body;
    
    if (!driverId || !newPassenger) {
      return res.status(400).json({
        success: false,
        message: 'Driver ID and new passenger data are required'
      });
    }
    
    const analysis = await aiRideSharingService.analyzeOptimalRoute(driverId, newPassenger);
    
    res.json({
      success: true,
      analysis
    });
  } catch (error) {
    logger.error(`Error analyzing optimal route: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error analyzing route: ${error.message}`
    });
  }
});

/**
 * Suggest additional passengers for an active ride
 */
router.get('/ride-sharing/suggestions/:rideId', async (req, res) => {
  try {
    const { rideId } = req.params;
    const { radius = 5, limit = 5 } = req.query;
    
    const suggestions = await aiRideSharingService.suggestAdditionalPassengers(rideId, {
      radius: parseFloat(radius),
      limit: parseInt(limit)
    });
    
    res.json({
      success: true,
      suggestions
    });
  } catch (error) {
    logger.error(`Error getting passenger suggestions: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error getting suggestions: ${error.message}`
    });
  }
});

/**
 * Send intelligent notifications for ride sharing
 */
router.post('/ride-sharing/notify', async (req, res) => {
  try {
    const { rideId, suggestions } = req.body;
    
    if (!rideId || !suggestions) {
      return res.status(400).json({
        success: false,
        message: 'Ride ID and suggestions are required'
      });
    }
    
    await aiRideSharingService.sendIntelligentNotifications(rideId, suggestions, req.io);
    
    res.json({
      success: true,
      message: 'Notifications sent successfully'
    });
  } catch (error) {
    logger.error(`Error sending notifications: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error sending notifications: ${error.message}`
    });
  }
});

/**
 * Compare available rides for a passenger
 */
router.post('/ride-sharing/compare-rides', async (req, res) => {
  try {
    const { passengerRequest } = req.body;
    
    if (!passengerRequest) {
      return res.status(400).json({
        success: false,
        message: 'Passenger request data is required'
      });
    }
    
    const rideOptions = await aiRideSharingService.compareAvailableRides(passengerRequest);
    
    res.json({
      success: true,
      rideOptions
    });
  } catch (error) {
    logger.error(`Error comparing rides: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error comparing rides: ${error.message}`
    });
  }
});

// Recommendation System Routes

/**
 * Get rider recommendations for a driver
 */
router.get('/recommendations/riders/:driverId', async (req, res) => {
  try {
    const { driverId } = req.params;
    const { limit = 10 } = req.query;
    
    // Get available riders (this would come from a queue or database)
    const availableRiders = await getAvailableRiders();
    
    const recommendations = await recommendationService.recommendBestRiders(
      driverId, 
      availableRiders, 
      { limit: parseInt(limit) }
    );
    
    res.json({
      success: true,
      recommendations
    });
  } catch (error) {
    logger.error(`Error getting rider recommendations: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error getting recommendations: ${error.message}`
    });
  }
});

/**
 * Get driver recommendations for a rider
 */
router.get('/recommendations/drivers/:riderId', async (req, res) => {
  try {
    const { riderId } = req.params;
    const { limit = 10 } = req.query;
    
    // Get available drivers
    const availableDrivers = await dbService.getDrivers({ isAvailable: true });
    
    const recommendations = await recommendationService.recommendBestDrivers(
      riderId, 
      availableDrivers, 
      { limit: parseInt(limit) }
    );
    
    res.json({
      success: true,
      recommendations
    });
  } catch (error) {
    logger.error(`Error getting driver recommendations: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error getting recommendations: ${error.message}`
    });
  }
});

/**
 * Learn from user interaction
 */
router.post('/recommendations/learn', async (req, res) => {
  try {
    const { userId, userType, interaction } = req.body;
    
    if (!userId || !userType || !interaction) {
      return res.status(400).json({
        success: false,
        message: 'User ID, user type, and interaction data are required'
      });
    }
    
    recommendationService.learnFromInteraction(userId, userType, interaction);
    
    res.json({
      success: true,
      message: 'Interaction learned successfully'
    });
  } catch (error) {
    logger.error(`Error learning from interaction: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error learning from interaction: ${error.message}`
    });
  }
});

// Safety Features Routes

/**
 * Verify user identity with face recognition
 */
router.post('/safety/verify-identity', async (req, res) => {
  try {
    const { userId, userType, imageData } = req.body;
    
    if (!userId || !userType || !imageData) {
      return res.status(400).json({
        success: false,
        message: 'User ID, user type, and image data are required'
      });
    }
    
    const verificationResult = await safetyService.verifyUserIdentity(userId, userType, imageData);
    
    res.json({
      success: true,
      verification: verificationResult
    });
  } catch (error) {
    logger.error(`Error verifying identity: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error verifying identity: ${error.message}`
    });
  }
});

/**
 * Set gender preference for safety
 */
router.post('/safety/gender-preference', async (req, res) => {
  try {
    const { userId, preference } = req.body;
    
    if (!userId || !preference) {
      return res.status(400).json({
        success: false,
        message: 'User ID and preference are required'
      });
    }
    
    safetyService.setGenderPreference(userId, preference);
    
    res.json({
      success: true,
      message: 'Gender preference set successfully'
    });
  } catch (error) {
    logger.error(`Error setting gender preference: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error setting preference: ${error.message}`
    });
  }
});

/**
 * Add emergency contact
 */
router.post('/safety/emergency-contact', async (req, res) => {
  try {
    const { userId, contact } = req.body;
    
    if (!userId || !contact) {
      return res.status(400).json({
        success: false,
        message: 'User ID and contact information are required'
      });
    }
    
    safetyService.addEmergencyContact(userId, contact);
    
    res.json({
      success: true,
      message: 'Emergency contact added successfully'
    });
  } catch (error) {
    logger.error(`Error adding emergency contact: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error adding contact: ${error.message}`
    });
  }
});

/**
 * Start safety monitoring for a ride
 */
router.post('/safety/monitor/:rideId', async (req, res) => {
  try {
    const { rideId } = req.params;
    
    await safetyService.monitorRideSafety(rideId, req.io);
    
    res.json({
      success: true,
      message: 'Safety monitoring started'
    });
  } catch (error) {
    logger.error(`Error starting safety monitoring: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error starting monitoring: ${error.message}`
    });
  }
});

// Voice Assistant Routes

/**
 * Process voice command
 */
router.post('/voice/command', async (req, res) => {
  try {
    const { command, userId, userType } = req.body;

    if (!command || !userId || !userType) {
      return res.status(400).json({
        success: false,
        message: 'Command, user ID, and user type are required'
      });
    }

    const result = await voiceAssistantService.processVoiceCommand(command, userId, userType);

    res.json(result);
  } catch (error) {
    logger.error(`Error processing voice command: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error processing command: ${error.message}`
    });
  }
});

/**
 * Get supported voice commands
 */
router.get('/voice/commands', (req, res) => {
  try {
    const { userType } = req.query;

    const driverCommands = [
      'البحث عن راكب',
      'قبول الراكب',
      'رفض الراكب',
      'حالة الرحلة',
      'مشاركة الرحلة',
      'مساعدة'
    ];

    const riderCommands = [
      'طلب رحلة',
      'إلغاء الرحلة',
      'أين السائق',
      'تغيير الوجهة',
      'مشاركة الرحلة',
      'تقييم الرحلة',
      'مساعدة'
    ];

    const commands = userType === 'driver' ? driverCommands : riderCommands;

    res.json({
      success: true,
      commands,
      userType: userType || 'rider'
    });
  } catch (error) {
    logger.error(`Error getting voice commands: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error getting commands: ${error.message}`
    });
  }
});

// Helper function to get available riders (placeholder)
async function getAvailableRiders() {
  // In a real implementation, this would query a database or queue
  // For now, return empty array
  return [];
}

module.exports = router;
