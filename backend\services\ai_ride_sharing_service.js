/**
 * AI-Powered Ride Sharing Service
 * 
 * This service implements advanced AI features for ride sharing including:
 * - Optimal route analysis for multiple passengers
 * - Intelligent passenger matching
 * - Real-time notifications and recommendations
 * - Safety features with gender-based separation
 */

const axios = require('axios');
const logger = require('../utils/logger');
const dbService = require('./database_service');
const aiService = require('./ai_service');

class AIRideSharingService {
  constructor() {
    this.activeRides = new Map(); // Store active rides for quick access
    this.passengerQueue = new Map(); // Queue of passengers waiting for rides
    this.driverPreferences = new Map(); // Store driver preferences
    this.passengerPreferences = new Map(); // Store passenger preferences
    
    logger.info('AI Ride Sharing Service initialized');
  }

  /**
   * Analyze optimal route for adding a new passenger to existing ride
   * @param {string} driverId - ID of the driver
   * @param {Object} newPassenger - New passenger data
   * @returns {Promise<Object>} - Analysis result with recommendations
   */
  async analyzeOptimalRoute(driverId, newPassenger) {
    try {
      // Get current active ride for the driver
      const currentRide = await this.getCurrentRideForDriver(driverId);
      
      if (!currentRide) {
        return {
          canAccept: true,
          recommendation: 'accept',
          message: 'No current ride, can accept new passenger',
          routeOptimization: null
        };
      }

      // Calculate original route metrics
      const originalMetrics = await this.calculateRouteMetrics(currentRide);
      
      // Create temporary route with new passenger
      const tempRoute = await this.createTempRouteWithNewPassenger(currentRide, newPassenger);
      const newMetrics = await this.calculateRouteMetrics(tempRoute);
      
      // Analyze route efficiency
      const analysis = this.analyzeRouteEfficiency(originalMetrics, newMetrics, newPassenger);
      
      // Check passenger compatibility
      const compatibilityScore = await this.checkPassengerCompatibility(currentRide, newPassenger);
      
      // Generate AI recommendation
      const recommendation = await this.generateAIRecommendation(analysis, compatibilityScore);
      
      return {
        canAccept: recommendation.accept,
        recommendation: recommendation.action,
        message: recommendation.message,
        routeOptimization: {
          originalDistance: originalMetrics.distance,
          newDistance: newMetrics.distance,
          detourDistance: newMetrics.distance - originalMetrics.distance,
          originalDuration: originalMetrics.duration,
          newDuration: newMetrics.duration,
          detourDuration: newMetrics.duration - originalMetrics.duration,
          efficiencyScore: analysis.efficiencyScore,
          compatibilityScore: compatibilityScore
        },
        fareOptimization: this.calculateFareOptimization(originalMetrics, newMetrics, newPassenger),
        environmentalImpact: this.calculateEnvironmentalImpact(newPassenger)
      };
    } catch (error) {
      logger.error(`Error analyzing optimal route: ${error.message}`);
      throw error;
    }
  }

  /**
   * Find and suggest additional passengers for an active ride
   * @param {string} rideId - ID of the active ride
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of suggested passengers
   */
  async suggestAdditionalPassengers(rideId, options = {}) {
    try {
      const ride = await dbService.getRideById(rideId);
      if (!ride) {
        throw new Error(`Ride ${rideId} not found`);
      }

      // Get waiting passengers in the area
      const waitingPassengers = await this.getWaitingPassengersInArea(ride, options.radius || 5);
      
      const suggestions = [];
      
      for (const passenger of waitingPassengers) {
        // Analyze compatibility and route efficiency
        const analysis = await this.analyzeOptimalRoute(ride.driverId, passenger);
        
        if (analysis.canAccept && analysis.routeOptimization.efficiencyScore > 0.7) {
          suggestions.push({
            passenger,
            analysis,
            priority: this.calculateSuggestionPriority(analysis),
            estimatedPickupTime: this.calculateEstimatedPickupTime(ride, passenger),
            fareReduction: analysis.fareOptimization.savings
          });
        }
      }
      
      // Sort by priority (highest first)
      suggestions.sort((a, b) => b.priority - a.priority);
      
      return suggestions.slice(0, options.limit || 5);
    } catch (error) {
      logger.error(`Error suggesting additional passengers: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send intelligent notifications for ride sharing opportunities
   * @param {string} rideId - ID of the ride
   * @param {Array} suggestions - Array of passenger suggestions
   * @param {Object} io - Socket.IO instance
   */
  async sendIntelligentNotifications(rideId, suggestions, io) {
    try {
      const ride = await dbService.getRideById(rideId);
      
      // Notify driver about passenger suggestions
      if (suggestions.length > 0) {
        io.to(`driver_${ride.driverId}`).emit('ride:passenger_suggestions', {
          rideId,
          suggestions: suggestions.map(s => ({
            passengerId: s.passenger.id,
            pickupLocation: s.passenger.pickupLocation,
            destination: s.passenger.destination,
            estimatedPickupTime: s.estimatedPickupTime,
            fareReduction: s.fareReduction,
            efficiencyScore: s.analysis.routeOptimization.efficiencyScore,
            environmentalBenefit: s.analysis.environmentalImpact
          })),
          timestamp: new Date().toISOString()
        });
      }
      
      // Notify suggested passengers about ride sharing opportunity
      for (const suggestion of suggestions) {
        io.to(`passenger_${suggestion.passenger.id}`).emit('ride:sharing_opportunity', {
          rideId,
          driverId: ride.driverId,
          currentRoute: ride.route,
          estimatedPickupTime: suggestion.estimatedPickupTime,
          fareReduction: suggestion.fareReduction,
          environmentalBenefit: suggestion.analysis.environmentalImpact,
          timestamp: new Date().toISOString()
        });
      }
      
      logger.info(`Sent intelligent notifications for ride ${rideId} to ${suggestions.length} passengers`);
    } catch (error) {
      logger.error(`Error sending intelligent notifications: ${error.message}`);
      throw error;
    }
  }

  /**
   * Determine optimal pickup and drop-off points for additional passengers
   * @param {Object} currentRoute - Current ride route
   * @param {Object} newPassenger - New passenger data
   * @returns {Promise<Object>} - Optimal pickup and drop-off points
   */
  async determineOptimalPickupDropoff(currentRoute, newPassenger) {
    try {
      const pickupOptions = await this.findNearbyPickupPoints(
        newPassenger.pickupLocation,
        currentRoute,
        500 // 500 meters radius
      );
      
      const dropoffOptions = await this.findNearbyDropoffPoints(
        newPassenger.destination,
        currentRoute,
        500 // 500 meters radius
      );
      
      // Select optimal points based on route efficiency
      const optimalPickup = this.selectOptimalPoint(pickupOptions, currentRoute, 'pickup');
      const optimalDropoff = this.selectOptimalPoint(dropoffOptions, currentRoute, 'dropoff');
      
      return {
        pickup: {
          original: newPassenger.pickupLocation,
          optimal: optimalPickup,
          walkingDistance: this.calculateDistance(newPassenger.pickupLocation, optimalPickup),
          walkingTime: this.calculateWalkingTime(newPassenger.pickupLocation, optimalPickup)
        },
        dropoff: {
          original: newPassenger.destination,
          optimal: optimalDropoff,
          walkingDistance: this.calculateDistance(newPassenger.destination, optimalDropoff),
          walkingTime: this.calculateWalkingTime(newPassenger.destination, optimalDropoff)
        }
      };
    } catch (error) {
      logger.error(`Error determining optimal pickup/dropoff: ${error.message}`);
      throw error;
    }
  }

  /**
   * Compare available rides in the same area and recommend the best option
   * @param {Object} passengerRequest - Passenger's ride request
   * @returns {Promise<Array>} - Array of ride options ranked by suitability
   */
  async compareAvailableRides(passengerRequest) {
    try {
      // Get all active rides in the area
      const nearbyRides = await this.getNearbyActiveRides(
        passengerRequest.pickupLocation,
        10 // 10 km radius
      );
      
      const rideOptions = [];
      
      for (const ride of nearbyRides) {
        // Analyze compatibility with this ride
        const analysis = await this.analyzeOptimalRoute(ride.driverId, passengerRequest);
        
        if (analysis.canAccept) {
          const suitabilityScore = this.calculateRideSuitability(ride, passengerRequest, analysis);
          
          rideOptions.push({
            rideId: ride.id,
            driverId: ride.driverId,
            analysis,
            suitabilityScore,
            estimatedPickupTime: this.calculateEstimatedPickupTime(ride, passengerRequest),
            fareEstimate: analysis.fareOptimization.passengerFare,
            comfortLevel: this.assessComfortLevel(ride, passengerRequest)
          });
        }
      }
      
      // Sort by suitability score (highest first)
      rideOptions.sort((a, b) => b.suitabilityScore - a.suitabilityScore);
      
      return rideOptions;
    } catch (error) {
      logger.error(`Error comparing available rides: ${error.message}`);
      throw error;
    }
  }

  /**
   * Select the best passengers for a ride based on multiple criteria
   * @param {string} rideId - ID of the ride
   * @param {Array} candidatePassengers - Array of candidate passengers
   * @param {number} maxPassengers - Maximum number of passengers to select
   * @returns {Promise<Array>} - Selected passengers
   */
  async selectBestPassengers(rideId, candidatePassengers, maxPassengers = 3) {
    try {
      const ride = await dbService.getRideById(rideId);
      const selectedPassengers = [];
      let currentRoute = ride.route;
      
      // Sort candidates by priority
      const prioritizedCandidates = candidatePassengers
        .map(passenger => ({
          ...passenger,
          priority: this.calculatePassengerPriority(passenger, ride)
        }))
        .sort((a, b) => b.priority - a.priority);
      
      for (const candidate of prioritizedCandidates) {
        if (selectedPassengers.length >= maxPassengers) break;
        
        // Check if adding this passenger improves the overall route
        const tempRoute = await this.createTempRouteWithPassengers(
          currentRoute,
          [...selectedPassengers, candidate]
        );
        
        const routeAnalysis = await this.analyzeRouteWithMultiplePassengers(tempRoute);
        
        if (routeAnalysis.isOptimal) {
          selectedPassengers.push(candidate);
          currentRoute = tempRoute;
        }
      }
      
      return selectedPassengers;
    } catch (error) {
      logger.error(`Error selecting best passengers: ${error.message}`);
      throw error;
    }
  }

  // Helper methods

  async getCurrentRideForDriver(driverId) {
    const rides = await dbService.getRides({
      driverId,
      status: ['accepted', 'in_progress']
    });
    return rides.length > 0 ? rides[0] : null;
  }

  async calculateRouteMetrics(route) {
    if (!route || !route.stops || route.stops.length < 2) {
      return { distance: 0, duration: 0 };
    }

    // Use OSRM to calculate actual route metrics
    const waypoints = route.stops
      .sort((a, b) => a.sequenceOrder - b.sequenceOrder)
      .map(stop => `${stop.location.lng},${stop.location.lat}`)
      .join(';');

    try {
      const response = await axios.get(
        `https://router.project-osrm.org/route/v1/driving/${waypoints}?overview=false`
      );

      if (response.data.code === 'Ok' && response.data.routes.length > 0) {
        return {
          distance: response.data.routes[0].distance,
          duration: response.data.routes[0].duration
        };
      }
    } catch (error) {
      logger.warn(`OSRM route calculation failed: ${error.message}`);
    }

    // Fallback to simple distance calculation
    let totalDistance = 0;
    for (let i = 0; i < route.stops.length - 1; i++) {
      totalDistance += this.calculateDistance(
        route.stops[i].location,
        route.stops[i + 1].location
      );
    }

    return {
      distance: totalDistance * 1000, // Convert to meters
      duration: (totalDistance / 30) * 3600 // Assume 30 km/h average speed
    };
  }

  async createTempRouteWithNewPassenger(currentRoute, newPassenger) {
    const tempRoute = JSON.parse(JSON.stringify(currentRoute));

    // Add pickup stop
    tempRoute.stops.push({
      type: 'pickup',
      riderId: newPassenger.id,
      location: newPassenger.pickupLocation,
      sequenceOrder: tempRoute.stops.length,
      status: 'pending'
    });

    // Add dropoff stop
    tempRoute.stops.push({
      type: 'dropoff',
      riderId: newPassenger.id,
      location: newPassenger.destination,
      sequenceOrder: tempRoute.stops.length,
      status: 'pending'
    });

    // Optimize stop sequence
    this.optimizeStopSequence(tempRoute);

    return tempRoute;
  }

  optimizeStopSequence(route) {
    // Simple optimization: ensure pickups come before dropoffs for the same rider
    const optimizedStops = [];
    const riderStops = new Map();

    // Group stops by rider
    route.stops.forEach(stop => {
      if (!riderStops.has(stop.riderId)) {
        riderStops.set(stop.riderId, []);
      }
      riderStops.get(stop.riderId).push(stop);
    });

    // Add stops in optimized order
    let sequenceOrder = 0;
    riderStops.forEach(stops => {
      const pickup = stops.find(s => s.type === 'pickup');
      const dropoff = stops.find(s => s.type === 'dropoff');

      if (pickup) {
        pickup.sequenceOrder = sequenceOrder++;
        optimizedStops.push(pickup);
      }
      if (dropoff) {
        dropoff.sequenceOrder = sequenceOrder++;
        optimizedStops.push(dropoff);
      }
    });

    route.stops = optimizedStops;
  }

  analyzeRouteEfficiency(originalMetrics, newMetrics, newPassenger) {
    const detourRatio = (newMetrics.distance - originalMetrics.distance) / originalMetrics.distance;
    const timeIncrease = (newMetrics.duration - originalMetrics.duration) / originalMetrics.duration;

    // Calculate efficiency score (0-1, higher is better)
    let efficiencyScore = 1.0;

    // Penalize excessive detours
    if (detourRatio > 0.3) efficiencyScore -= 0.4;
    else if (detourRatio > 0.2) efficiencyScore -= 0.2;
    else if (detourRatio > 0.1) efficiencyScore -= 0.1;

    // Penalize excessive time increases
    if (timeIncrease > 0.4) efficiencyScore -= 0.3;
    else if (timeIncrease > 0.2) efficiencyScore -= 0.15;

    return {
      efficiencyScore: Math.max(0, efficiencyScore),
      detourRatio,
      timeIncrease,
      recommendation: efficiencyScore > 0.6 ? 'accept' : 'reject'
    };
  }

  async checkPassengerCompatibility(currentRide, newPassenger) {
    let compatibilityScore = 1.0;

    // Check gender preferences for safety
    if (newPassenger.preferences?.genderPreference) {
      const currentPassengers = await this.getCurrentPassengers(currentRide);
      const genderMatch = this.checkGenderCompatibility(currentPassengers, newPassenger);
      if (!genderMatch) compatibilityScore -= 0.5;
    }

    // Check smoking preferences
    if (newPassenger.preferences?.noSmoking) {
      const currentPassengers = await this.getCurrentPassengers(currentRide);
      const smokingConflict = currentPassengers.some(p => p.preferences?.smoking);
      if (smokingConflict) compatibilityScore -= 0.2;
    }

    // Check music preferences
    if (newPassenger.preferences?.musicPreference) {
      // This could be expanded based on actual preferences
      compatibilityScore += 0.1;
    }

    return Math.max(0, Math.min(1, compatibilityScore));
  }

  async generateAIRecommendation(analysis, compatibilityScore) {
    const overallScore = (analysis.efficiencyScore * 0.7) + (compatibilityScore * 0.3);

    let recommendation = {
      accept: false,
      action: 'reject',
      message: 'Route not optimal for ride sharing'
    };

    if (overallScore > 0.8) {
      recommendation = {
        accept: true,
        action: 'accept',
        message: 'Excellent match - highly recommended'
      };
    } else if (overallScore > 0.6) {
      recommendation = {
        accept: true,
        action: 'accept_with_conditions',
        message: 'Good match - acceptable with minor detour'
      };
    } else if (overallScore > 0.4) {
      recommendation = {
        accept: false,
        action: 'consider',
        message: 'Marginal match - consider passenger preferences'
      };
    }

    return recommendation;
  }

  calculateDistance(point1, point2) {
    const R = 6371; // Earth's radius in km
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLon = (point2.lng - point1.lng) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
}

module.exports = new AIRideSharingService();
