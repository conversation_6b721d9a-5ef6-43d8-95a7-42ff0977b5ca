/**
 * Voice Assistant Service
 * 
 * This service implements voice command processing and natural language understanding
 * for the ride sharing application. It integrates with the existing voice assistant
 * and provides enhanced AI-powered voice interactions.
 */

const axios = require('axios');
const logger = require('../utils/logger');
const dbService = require('./database_service');
const aiRideSharingService = require('./ai_ride_sharing_service');

class VoiceAssistantService {
  constructor() {
    this.voiceApiUrl = process.env.VOICE_API_URL || 'http://localhost:5000';
    this.supportedCommands = [
      'طلب رحلة',
      'إلغاء الرحلة',
      'أين السائق',
      'تغيير الوجهة',
      'مشاركة الرحلة',
      'تقييم الرحلة',
      'البحث عن راكب',
      'قبول الراكب',
      'رفض الراكب',
      'حالة الرحلة',
      'مساعدة'
    ];
    
    logger.info('Voice Assistant Service initialized');
  }

  /**
   * Process voice command and return appropriate response
   * @param {string} command - Voice command text
   * @param {string} userId - ID of the user
   * @param {string} userType - Type of user ('driver' or 'rider')
   * @returns {Promise<Object>} - Response object
   */
  async processVoiceCommand(command, userId, userType) {
    try {
      const normalizedCommand = command.trim().toLowerCase();
      
      // Analyze command intent
      const intent = this.analyzeCommandIntent(normalizedCommand);
      
      // Process based on intent
      let response;
      switch (intent.action) {
        case 'request_ride':
          response = await this.handleRideRequest(intent, userId);
          break;
        case 'cancel_ride':
          response = await this.handleRideCancel(intent, userId);
          break;
        case 'driver_location':
          response = await this.handleDriverLocationQuery(intent, userId);
          break;
        case 'change_destination':
          response = await this.handleDestinationChange(intent, userId);
          break;
        case 'share_ride':
          response = await this.handleRideSharing(intent, userId, userType);
          break;
        case 'rate_ride':
          response = await this.handleRideRating(intent, userId);
          break;
        case 'find_passenger':
          response = await this.handlePassengerSearch(intent, userId);
          break;
        case 'accept_passenger':
          response = await this.handlePassengerAccept(intent, userId);
          break;
        case 'reject_passenger':
          response = await this.handlePassengerReject(intent, userId);
          break;
        case 'ride_status':
          response = await this.handleRideStatusQuery(intent, userId);
          break;
        case 'help':
          response = this.handleHelpRequest(intent, userType);
          break;
        default:
          response = this.handleUnknownCommand(command);
      }
      
      return {
        success: true,
        response: response.message,
        action: intent.action,
        data: response.data || null
      };
      
    } catch (error) {
      logger.error(`Error processing voice command: ${error.message}`);
      return {
        success: false,
        response: 'عذراً، حدث خطأ في معالجة الأمر. يرجى المحاولة مرة أخرى.',
        error: error.message
      };
    }
  }

  /**
   * Analyze command intent using NLP
   * @param {string} command - Normalized command text
   * @returns {Object} - Intent analysis result
   */
  analyzeCommandIntent(command) {
    const intents = {
      'طلب رحلة': { action: 'request_ride', confidence: 0.9 },
      'إلغاء الرحلة': { action: 'cancel_ride', confidence: 0.9 },
      'أين السائق': { action: 'driver_location', confidence: 0.9 },
      'تغيير الوجهة': { action: 'change_destination', confidence: 0.8 },
      'مشاركة الرحلة': { action: 'share_ride', confidence: 0.8 },
      'تقييم الرحلة': { action: 'rate_ride', confidence: 0.8 },
      'البحث عن راكب': { action: 'find_passenger', confidence: 0.8 },
      'قبول الراكب': { action: 'accept_passenger', confidence: 0.9 },
      'رفض الراكب': { action: 'reject_passenger', confidence: 0.9 },
      'حالة الرحلة': { action: 'ride_status', confidence: 0.8 },
      'مساعدة': { action: 'help', confidence: 0.9 }
    };
    
    // Find best matching intent
    let bestMatch = { action: 'unknown', confidence: 0 };
    
    for (const [pattern, intent] of Object.entries(intents)) {
      if (command.includes(pattern)) {
        if (intent.confidence > bestMatch.confidence) {
          bestMatch = intent;
        }
      }
    }
    
    // Extract entities (locations, numbers, etc.)
    const entities = this.extractEntities(command);
    
    return {
      ...bestMatch,
      entities,
      originalCommand: command
    };
  }

  /**
   * Extract entities from command text
   * @param {string} command - Command text
   * @returns {Object} - Extracted entities
   */
  extractEntities(command) {
    const entities = {
      locations: [],
      numbers: [],
      time: null
    };
    
    // Extract numbers
    const numberMatches = command.match(/\d+/g);
    if (numberMatches) {
      entities.numbers = numberMatches.map(n => parseInt(n));
    }
    
    // Extract common location keywords
    const locationKeywords = ['من', 'إلى', 'في', 'عند', 'قرب'];
    locationKeywords.forEach(keyword => {
      const index = command.indexOf(keyword);
      if (index !== -1) {
        // Extract text after location keyword
        const afterKeyword = command.substring(index + keyword.length).trim();
        const words = afterKeyword.split(' ').slice(0, 3); // Take up to 3 words
        if (words.length > 0) {
          entities.locations.push(words.join(' '));
        }
      }
    });
    
    return entities;
  }

  /**
   * Handle ride request command
   */
  async handleRideRequest(intent, userId) {
    try {
      // Check if user already has an active ride
      const activeRides = await dbService.getRides({ 
        riderId: userId, 
        status: ['pending', 'accepted', 'in_progress'] 
      });
      
      if (activeRides.length > 0) {
        return {
          message: 'لديك رحلة نشطة بالفعل. يرجى إنهاء الرحلة الحالية أولاً.',
          data: { activeRide: activeRides[0] }
        };
      }
      
      // If locations are provided in the command, use them
      if (intent.entities.locations.length >= 2) {
        const pickup = intent.entities.locations[0];
        const destination = intent.entities.locations[1];
        
        return {
          message: `جاري البحث عن سائق من ${pickup} إلى ${destination}...`,
          data: { pickup, destination, needsConfirmation: true }
        };
      }
      
      return {
        message: 'من أين تريد الانطلاق؟',
        data: { step: 'pickup_location' }
      };
      
    } catch (error) {
      logger.error(`Error handling ride request: ${error.message}`);
      return {
        message: 'عذراً، حدث خطأ في طلب الرحلة. يرجى المحاولة مرة أخرى.'
      };
    }
  }

  /**
   * Handle ride sharing command
   */
  async handleRideSharing(intent, userId, userType) {
    try {
      if (userType === 'driver') {
        // Driver wants to find passengers to share ride with
        const activeRides = await dbService.getRides({ 
          driverId: userId, 
          status: ['accepted', 'in_progress'] 
        });
        
        if (activeRides.length === 0) {
          return {
            message: 'ليس لديك رحلة نشطة حالياً لمشاركتها.'
          };
        }
        
        const ride = activeRides[0];
        const suggestions = await aiRideSharingService.suggestAdditionalPassengers(ride.id);
        
        if (suggestions.length === 0) {
          return {
            message: 'لا يوجد ركاب متاحين للمشاركة في منطقتك حالياً.'
          };
        }
        
        return {
          message: `وجدت ${suggestions.length} راكب متاح للمشاركة. هل تريد مراجعة الاقتراحات؟`,
          data: { suggestions: suggestions.slice(0, 3) } // Limit to 3 for voice
        };
        
      } else {
        // Rider wants to join a shared ride
        const availableRides = await this.findAvailableSharedRides(userId);
        
        if (availableRides.length === 0) {
          return {
            message: 'لا توجد رحلات مشتركة متاحة في منطقتك حالياً.'
          };
        }
        
        return {
          message: `وجدت ${availableRides.length} رحلة مشتركة متاحة. هل تريد الانضمام لإحداها؟`,
          data: { availableRides: availableRides.slice(0, 3) }
        };
      }
      
    } catch (error) {
      logger.error(`Error handling ride sharing: ${error.message}`);
      return {
        message: 'عذراً، حدث خطأ في البحث عن رحلات مشتركة.'
      };
    }
  }

  /**
   * Handle driver location query
   */
  async handleDriverLocationQuery(intent, userId) {
    try {
      const activeRides = await dbService.getRides({ 
        riderId: userId, 
        status: ['accepted', 'in_progress'] 
      });
      
      if (activeRides.length === 0) {
        return {
          message: 'ليس لديك رحلة نشطة حالياً.'
        };
      }
      
      const ride = activeRides[0];
      if (!ride.driverId) {
        return {
          message: 'لم يتم تعيين سائق لرحلتك بعد.'
        };
      }
      
      // In a real implementation, this would get real-time driver location
      return {
        message: 'السائق في طريقه إليك. الوقت المتوقع للوصول: 5 دقائق.',
        data: { 
          driverId: ride.driverId,
          estimatedArrival: '5 دقائق',
          status: ride.status
        }
      };
      
    } catch (error) {
      logger.error(`Error handling driver location query: ${error.message}`);
      return {
        message: 'عذراً، لا يمكن الحصول على موقع السائق حالياً.'
      };
    }
  }

  /**
   * Handle help request
   */
  handleHelpRequest(intent, userType) {
    const commands = userType === 'driver' ? 
      ['البحث عن راكب', 'قبول الراكب', 'رفض الراكب', 'حالة الرحلة'] :
      ['طلب رحلة', 'إلغاء الرحلة', 'أين السائق', 'مشاركة الرحلة'];
    
    return {
      message: `يمكنك استخدام الأوامر التالية: ${commands.join('، ')}`,
      data: { availableCommands: commands }
    };
  }

  /**
   * Handle unknown command
   */
  handleUnknownCommand(command) {
    return {
      message: 'عذراً، لم أفهم الأمر. قل "مساعدة" لمعرفة الأوامر المتاحة.',
      data: { unknownCommand: command }
    };
  }

  /**
   * Send voice command to external voice API
   */
  async sendToVoiceAPI(endpoint, data) {
    try {
      const response = await axios.post(`${this.voiceApiUrl}/api/voice/${endpoint}`, data);
      return response.data;
    } catch (error) {
      logger.error(`Error calling voice API: ${error.message}`);
      throw error;
    }
  }

  // Additional helper methods would be implemented here...
  async findAvailableSharedRides(userId) {
    // Placeholder implementation
    return [];
  }

  async handleRideCancel(intent, userId) {
    return { message: 'تم إلغاء الرحلة بنجاح.' };
  }

  async handleDestinationChange(intent, userId) {
    return { message: 'يرجى تحديد الوجهة الجديدة.' };
  }

  async handleRideRating(intent, userId) {
    return { message: 'كيف تقيم رحلتك من 1 إلى 5؟' };
  }

  async handlePassengerSearch(intent, userId) {
    return { message: 'جاري البحث عن ركاب في منطقتك...' };
  }

  async handlePassengerAccept(intent, userId) {
    return { message: 'تم قبول الراكب بنجاح.' };
  }

  async handlePassengerReject(intent, userId) {
    return { message: 'تم رفض الراكب.' };
  }

  async handleRideStatusQuery(intent, userId) {
    return { message: 'رحلتك قيد التنفيذ.' };
  }
}

module.exports = new VoiceAssistantService();
