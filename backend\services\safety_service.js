/**
 * Safety Service with Face Recognition
 * 
 * This service implements safety features including:
 * - Gender-based ride separation for enhanced safety
 * - Face recognition for identity verification
 * - Safety monitoring and alerts
 * - Emergency response features
 */

const logger = require('../utils/logger');
const dbService = require('./database_service');

class SafetyService {
  constructor() {
    this.verifiedUsers = new Map(); // Store verified user data
    this.safetyAlerts = new Map(); // Store active safety alerts
    this.emergencyContacts = new Map(); // Store emergency contacts
    this.genderPreferences = new Map(); // Store gender preferences
    
    logger.info('Safety Service initialized');
  }

  /**
   * Verify user identity using face recognition
   * @param {string} userId - ID of the user
   * @param {string} userType - Type of user ('driver' or 'rider')
   * @param {string} imageData - Base64 encoded image data
   * @returns {Promise<Object>} - Verification result
   */
  async verifyUserIdentity(userId, userType, imageData) {
    try {
      // In a real implementation, this would integrate with a face recognition API
      // For now, we'll simulate the verification process
      
      const verificationResult = await this.performFaceRecognition(userId, imageData);
      
      if (verificationResult.success) {
        // Store verification data
        this.verifiedUsers.set(userId, {
          userType,
          verifiedAt: new Date(),
          confidence: verificationResult.confidence,
          faceData: verificationResult.faceData
        });
        
        logger.info(`User ${userId} identity verified successfully`);
        
        return {
          success: true,
          verified: true,
          confidence: verificationResult.confidence,
          message: 'Identity verified successfully'
        };
      } else {
        logger.warn(`User ${userId} identity verification failed`);
        
        return {
          success: false,
          verified: false,
          confidence: verificationResult.confidence,
          message: 'Identity verification failed'
        };
      }
    } catch (error) {
      logger.error(`Error verifying user identity: ${error.message}`);
      throw error;
    }
  }

  /**
   * Enforce gender-based ride separation for safety
   * @param {Object} rideRequest - Ride request data
   * @param {Array} availableDrivers - Available drivers
   * @returns {Promise<Array>} - Filtered drivers based on gender preferences
   */
  async enforceGenderSeparation(rideRequest, availableDrivers) {
    try {
      const rider = await this.getUserById(rideRequest.riderId);
      if (!rider) {
        return availableDrivers; // Return all if rider not found
      }

      const riderGender = rider.gender;
      const genderPreference = this.getGenderPreference(rideRequest.riderId);
      
      // If no gender preference set, return all drivers
      if (!genderPreference || genderPreference === 'any') {
        return availableDrivers;
      }
      
      // Filter drivers based on gender preference
      const filteredDrivers = [];
      
      for (const driver of availableDrivers) {
        const driverGender = driver.gender;
        
        // Check if driver matches gender preference
        if (this.isGenderCompatible(riderGender, driverGender, genderPreference)) {
          // Additional check: ensure no mixed-gender passengers in shared rides
          const hasCompatiblePassengers = await this.checkPassengerGenderCompatibility(
            driver.id, 
            riderGender, 
            genderPreference
          );
          
          if (hasCompatiblePassengers) {
            filteredDrivers.push(driver);
          }
        }
      }
      
      logger.info(`Filtered ${availableDrivers.length} drivers to ${filteredDrivers.length} based on gender preferences`);
      
      return filteredDrivers;
    } catch (error) {
      logger.error(`Error enforcing gender separation: ${error.message}`);
      return availableDrivers; // Return all drivers if error occurs
    }
  }

  /**
   * Monitor ride safety in real-time
   * @param {string} rideId - ID of the ride to monitor
   * @param {Object} io - Socket.IO instance for real-time communication
   */
  async monitorRideSafety(rideId, io) {
    try {
      const ride = await dbService.getRideById(rideId);
      if (!ride) {
        throw new Error(`Ride ${rideId} not found`);
      }

      // Set up safety monitoring
      const monitoringInterval = setInterval(async () => {
        try {
          // Check if ride is still active
          const currentRide = await dbService.getRideById(rideId);
          if (!currentRide || currentRide.status === 'completed' || currentRide.status === 'cancelled') {
            clearInterval(monitoringInterval);
            return;
          }

          // Perform safety checks
          const safetyStatus = await this.performSafetyChecks(currentRide);
          
          if (safetyStatus.alertLevel > 0) {
            // Send safety alert
            this.sendSafetyAlert(currentRide, safetyStatus, io);
          }
          
          // Check for emergency situations
          if (safetyStatus.emergency) {
            this.handleEmergency(currentRide, safetyStatus, io);
          }
          
        } catch (error) {
          logger.error(`Error in safety monitoring for ride ${rideId}: ${error.message}`);
        }
      }, 30000); // Check every 30 seconds
      
      logger.info(`Started safety monitoring for ride ${rideId}`);
      
    } catch (error) {
      logger.error(`Error starting safety monitoring: ${error.message}`);
      throw error;
    }
  }

  /**
   * Handle emergency situations
   * @param {Object} ride - Ride data
   * @param {Object} emergencyData - Emergency situation data
   * @param {Object} io - Socket.IO instance
   */
  async handleEmergency(ride, emergencyData, io) {
    try {
      const emergencyId = `emergency_${Date.now()}`;
      
      // Create emergency record
      const emergency = {
        id: emergencyId,
        rideId: ride.id,
        type: emergencyData.type,
        severity: emergencyData.severity,
        location: emergencyData.location,
        timestamp: new Date(),
        status: 'active'
      };
      
      // Store emergency alert
      this.safetyAlerts.set(emergencyId, emergency);
      
      // Notify all participants
      io.to(`ride_${ride.id}`).emit('safety:emergency', {
        emergencyId,
        type: emergencyData.type,
        message: 'Emergency situation detected',
        instructions: this.getEmergencyInstructions(emergencyData.type),
        timestamp: new Date().toISOString()
      });
      
      // Notify emergency contacts
      await this.notifyEmergencyContacts(ride, emergency);
      
      // In a real implementation, this would also notify authorities
      logger.error(`Emergency situation detected for ride ${ride.id}: ${emergencyData.type}`);
      
    } catch (error) {
      logger.error(`Error handling emergency: ${error.message}`);
    }
  }

  /**
   * Set gender preference for a user
   * @param {string} userId - ID of the user
   * @param {string} preference - Gender preference ('same', 'opposite', 'any')
   */
  setGenderPreference(userId, preference) {
    this.genderPreferences.set(userId, preference);
    logger.info(`Gender preference set for user ${userId}: ${preference}`);
  }

  /**
   * Get gender preference for a user
   * @param {string} userId - ID of the user
   * @returns {string} - Gender preference
   */
  getGenderPreference(userId) {
    return this.genderPreferences.get(userId) || 'any';
  }

  /**
   * Add emergency contact for a user
   * @param {string} userId - ID of the user
   * @param {Object} contact - Emergency contact information
   */
  addEmergencyContact(userId, contact) {
    if (!this.emergencyContacts.has(userId)) {
      this.emergencyContacts.set(userId, []);
    }
    
    this.emergencyContacts.get(userId).push({
      ...contact,
      addedAt: new Date()
    });
    
    logger.info(`Emergency contact added for user ${userId}`);
  }

  // Helper methods

  async performFaceRecognition(userId, imageData) {
    // Simulate face recognition API call
    // In a real implementation, this would integrate with services like:
    // - AWS Rekognition
    // - Azure Face API
    // - Google Cloud Vision API
    
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate recognition result
        const confidence = Math.random() * 0.3 + 0.7; // 70-100% confidence
        const success = confidence > 0.8;
        
        resolve({
          success,
          confidence,
          faceData: success ? `face_data_${userId}_${Date.now()}` : null
        });
      }, 1000);
    });
  }

  async getUserById(userId) {
    // Try to get from riders first, then drivers
    let user = await dbService.getRiderById(userId);
    if (!user) {
      user = await dbService.getDriverById(userId);
    }
    return user;
  }

  isGenderCompatible(riderGender, driverGender, preference) {
    switch (preference) {
      case 'same':
        return riderGender === driverGender;
      case 'opposite':
        return riderGender !== driverGender;
      case 'any':
      default:
        return true;
    }
  }

  async checkPassengerGenderCompatibility(driverId, newRiderGender, genderPreference) {
    // Get current passengers for this driver
    const activeRides = await dbService.getRides({ 
      driverId, 
      status: ['accepted', 'in_progress'] 
    });
    
    for (const ride of activeRides) {
      if (ride.stops) {
        for (const stop of ride.stops) {
          if (stop.type === 'pickup' && stop.status !== 'completed') {
            const passenger = await this.getUserById(stop.riderId);
            if (passenger && passenger.gender) {
              if (!this.isGenderCompatible(newRiderGender, passenger.gender, genderPreference)) {
                return false;
              }
            }
          }
        }
      }
    }
    
    return true;
  }

  async performSafetyChecks(ride) {
    // Simulate safety checks
    // In a real implementation, this would check:
    // - Route deviation
    // - Speed anomalies
    // - Unexpected stops
    // - Communication patterns
    
    return {
      alertLevel: 0, // 0 = safe, 1 = caution, 2 = warning, 3 = emergency
      emergency: false,
      checks: {
        routeDeviation: false,
        speedAnomaly: false,
        unexpectedStop: false,
        communicationIssue: false
      }
    };
  }

  sendSafetyAlert(ride, safetyStatus, io) {
    io.to(`ride_${ride.id}`).emit('safety:alert', {
      rideId: ride.id,
      alertLevel: safetyStatus.alertLevel,
      message: this.getSafetyMessage(safetyStatus.alertLevel),
      timestamp: new Date().toISOString()
    });
  }

  getSafetyMessage(alertLevel) {
    switch (alertLevel) {
      case 1: return 'Minor safety concern detected';
      case 2: return 'Safety warning - please be cautious';
      case 3: return 'Emergency situation detected';
      default: return 'All safety checks passed';
    }
  }

  getEmergencyInstructions(emergencyType) {
    const instructions = {
      'route_deviation': 'Contact driver immediately. Share location with emergency contacts.',
      'speed_anomaly': 'Ask driver to slow down. Prepare to exit safely if needed.',
      'unexpected_stop': 'Stay alert. Contact emergency services if you feel unsafe.',
      'communication_loss': 'Try to re-establish contact. Share your location with trusted contacts.'
    };
    
    return instructions[emergencyType] || 'Follow general safety protocols.';
  }

  async notifyEmergencyContacts(ride, emergency) {
    // Get all participants in the ride
    const participants = [];
    
    if (ride.stops) {
      for (const stop of ride.stops) {
        participants.push(stop.riderId);
      }
    }
    
    if (ride.driverId) {
      participants.push(ride.driverId);
    }
    
    // Notify emergency contacts for each participant
    for (const participantId of participants) {
      const contacts = this.emergencyContacts.get(participantId) || [];
      
      for (const contact of contacts) {
        // In a real implementation, this would send SMS/email/push notifications
        logger.info(`Emergency notification sent to ${contact.name} for user ${participantId}`);
      }
    }
  }
}

module.exports = new SafetyService();
